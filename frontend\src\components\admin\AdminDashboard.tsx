import React, { useState, useEffect } from 'react';
import { Loader2, Users, CreditCard, DollarSign, TrendingUp, TrendingDown, Calendar } from 'lucide-react';
import { api } from '../../lib/api';
import { useToast } from '../../contexts/ToastContext';

interface DashboardStats {
  total_users: number;
  active_subscriptions: number;
  total_revenue: number;
  monthly_revenue: number;
  cancelled_subscriptions: number;
}

export const AdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const toast = useToast();

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      const response = await api.get('/admin/dashboard');
      setStats(response.data);
    } catch (error) {
      console.error('Erro ao buscar estatísticas:', error);
      toast.error('Erro ao carregar estatísticas do dashboard');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">Erro ao carregar estatísticas</p>
      </div>
    );
  }

  const subscriptionRate = stats.total_users > 0 
    ? ((stats.active_subscriptions / stats.total_users) * 100).toFixed(1)
    : '0';

  const cancellationRate = (stats.active_subscriptions + stats.cancelled_subscriptions) > 0
    ? ((stats.cancelled_subscriptions / (stats.active_subscriptions + stats.cancelled_subscriptions)) * 100).toFixed(1)
    : '0';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Dashboard Administrativo
          </h1>
          <p className="text-gray-500 dark:text-gray-400">
            Visão geral das métricas e estatísticas da plataforma
          </p>
        </div>
        <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-200 text-gray-800">
          <Calendar className="h-4 w-4 mr-2" />
          {new Date().toLocaleDateString('pt-BR')}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* Total de Usuários */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-900">Total de Usuários</h3>
            <Users className="h-4 w-4 text-gray-400" />
          </div>
          <div className="text-2xl font-bold">{stats.total_users.toLocaleString()}</div>
          <p className="text-xs text-gray-500">
            Usuários registrados na plataforma
          </p>
        </div>

        {/* Assinaturas Ativas */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-900">Assinaturas Ativas</h3>
            <CreditCard className="h-4 w-4 text-gray-400" />
          </div>
          <div className="text-2xl font-bold text-green-600">
            {stats.active_subscriptions.toLocaleString()}
          </div>
          <p className="text-xs text-gray-500">
            Taxa de conversão: {subscriptionRate}%
          </p>
        </div>

        {/* Receita Total */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-900">Receita Total</h3>
            <DollarSign className="h-4 w-4 text-gray-400" />
          </div>
          <div className="text-2xl font-bold text-blue-600">
            {formatCurrency(stats.total_revenue)}
          </div>
          <p className="text-xs text-gray-500">
            Receita acumulada
          </p>
        </div>

        {/* Receita Mensal */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-900">Receita Mensal</h3>
            <TrendingUp className="h-4 w-4 text-gray-400" />
          </div>
          <div className="text-2xl font-bold text-purple-600">
            {formatCurrency(stats.monthly_revenue)}
          </div>
          <p className="text-xs text-gray-500">
            Receita recorrente mensal
          </p>
        </div>
      </div>

      {/* Métricas Adicionais */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Taxa de Conversão */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Taxa de Conversão</h3>
            <div className="text-sm text-gray-600">
              Porcentagem de usuários que se tornaram assinantes
            </div>
          </div>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="flex justify-between text-sm mb-1">
                  <span>Usuários Convertidos</span>
                  <span>{subscriptionRate}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(Number(subscriptionRate), 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>
            <div className="flex justify-between text-sm">
              <span>Assinantes: {stats.active_subscriptions}</span>
              <span>Total: {stats.total_users}</span>
            </div>
          </div>
        </div>

        {/* Cancelamentos */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Taxa de Cancelamento</h3>
            <div className="text-sm text-gray-600">
              Porcentagem de assinaturas canceladas
            </div>
          </div>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <TrendingDown className="h-8 w-8 text-red-500" />
              <div className="flex-1">
                <div className="text-2xl font-bold text-red-600">
                  {stats.cancelled_subscriptions.toLocaleString()}
                </div>
                <p className="text-sm text-gray-500">
                  Taxa: {cancellationRate}%
                </p>
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-red-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(Number(cancellationRate), 100)}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Métricas de Performance */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Resumo Financeiro</h3>
          <div className="text-sm text-gray-600">
            Visão geral do desempenho financeiro da plataforma
          </div>
        </div>
        <div className="grid gap-4 md:grid-cols-3">
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600 mb-1">
              {formatCurrency(stats.total_revenue)}
            </div>
            <div className="text-sm text-green-600 font-medium">Receita Total</div>
          </div>
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {formatCurrency(stats.monthly_revenue)}
            </div>
            <div className="text-sm text-blue-600 font-medium">MRR</div>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600 mb-1">
              {stats.active_subscriptions > 0 
                ? formatCurrency(stats.total_revenue / stats.active_subscriptions)
                : formatCurrency(0)
              }
            </div>
            <div className="text-sm text-purple-600 font-medium">Receita por Cliente</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
