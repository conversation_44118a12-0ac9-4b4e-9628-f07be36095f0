import { Kysely } from 'kysely';
import { Database } from '../database.types';
import { BaseRepository } from './base.repository';
import { IIdeaRepository } from './interfaces/idea.repository.interface';
import { CreateIdeaDto } from '../ideas/dto/create-idea.dto';
import { UpdateIdeaDto } from '../ideas/dto/update-idea.dto';
import { IdeaResponseDto } from '../ideas/dto/idea-response.dto';
export declare class IdeaRepository extends BaseRepository<IdeaResponseDto, CreateIdeaDto, UpdateIdeaDto> implements IIdeaRepository {
    constructor(db: Kysely<Database>);
    get tableName(): keyof Database;
    get entityName(): string;
    mapToResponseDto(entity: any, userTimezone?: string): IdeaResponseDto;
    prepareCreateData(dto: CreateIdeaDto, userId: number): any;
    prepareUpdateData(dto: UpdateIdeaDto): any;
    findAllWithCategory(userId: number, userTimezone: string, page?: number, limit?: number): Promise<{
        data: IdeaResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    toggleFavorite(id: number, userId: number): Promise<IdeaResponseDto>;
}
