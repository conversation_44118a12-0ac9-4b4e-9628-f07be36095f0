"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AgentWppService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentWppService = void 0;
const common_1 = require("@nestjs/common");
const database_types_1 = require("../database.types");
const financial_utils_1 = require("../common/utils/financial.utils");
const common_response_dto_1 = require("../common/dto/common-response.dto");
const tasks_service_1 = require("../tasks/tasks.service");
const finances_service_1 = require("../finances/finances.service");
const ideas_service_1 = require("../ideas/ideas.service");
const dashboard_service_1 = require("../dashboard/dashboard.service");
let AgentWppService = AgentWppService_1 = class AgentWppService {
    tasksService;
    financesService;
    ideasService;
    dashboardService;
    logger = new common_1.Logger(AgentWppService_1.name);
    db = database_types_1.db;
    cache = new Map();
    CACHE_TTL = 5 * 60 * 1000;
    constructor(tasksService, financesService, ideasService, dashboardService) {
        this.tasksService = tasksService;
        this.financesService = financesService;
        this.ideasService = ideasService;
        this.dashboardService = dashboardService;
    }
    validateAndNormalizePhone(phone) {
        if (!phone) {
            throw new common_1.BadRequestException('Telefone é obrigatório');
        }
        const normalized = phone.replace(/[^\d+]/g, '');
        if (!/^\+?[\d]{10,15}$/.test(normalized)) {
            throw new common_1.BadRequestException('Formato de telefone inválido. Use apenas números e + (opcional)');
        }
        const digitsOnly = normalized.replace('+', '');
        if (digitsOnly.length < 10 || digitsOnly.length > 15) {
            throw new common_1.BadRequestException('Telefone deve ter entre 10 e 15 dígitos');
        }
        return normalized;
    }
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
            return cached.data;
        }
        if (cached) {
            this.cache.delete(key);
        }
        return null;
    }
    setCache(key, data) {
        this.cache.set(key, { data, timestamp: Date.now() });
    }
    async getUserIdByPhone(phone) {
        try {
            const normalizedPhone = this.validateAndNormalizePhone(phone);
            const cacheKey = `user_phone_${normalizedPhone}`;
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                return cached;
            }
            const integration = await this.db
                .selectFrom('integrations_whatsapp')
                .select(['user_id', 'status'])
                .where('phone', '=', normalizedPhone)
                .where('status', 'in', ['pending', 'active'])
                .executeTakeFirst();
            if (!integration) {
                throw new common_1.ForbiddenException(`Usuário com telefone ${phone} não possui integração WhatsApp ativa`);
            }
            this.setCache(cacheKey, integration.user_id);
            return integration.user_id;
        }
        catch (error) {
            this.logger.error(`Erro ao buscar usuário por telefone ${phone}:`, error);
            throw error;
        }
    }
    async getUserTimezone(userId) {
        try {
            const cacheKey = `user_timezone_${userId}`;
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                return cached;
            }
            const user = await this.db
                .selectFrom('users')
                .select(['timezone'])
                .where('id', '=', userId)
                .executeTakeFirst();
            const timezone = user?.timezone || 'America/Sao_Paulo';
            this.setCache(cacheKey, timezone);
            return timezone;
        }
        catch (error) {
            this.logger.error(`Erro ao buscar timezone do usuário ${userId}:`, error);
            return 'America/Sao_Paulo';
        }
    }
    async checkIntegration(phone) {
        try {
            const normalizedPhone = this.validateAndNormalizePhone(phone);
            const cacheKey = `integration_check_${normalizedPhone}`;
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                return cached;
            }
            const integration = await this.db
                .selectFrom('integrations_whatsapp')
                .select(['user_id', 'status'])
                .where('phone', '=', normalizedPhone)
                .executeTakeFirst();
            let result;
            if (integration) {
                const user = await this.db
                    .selectFrom('users')
                    .select(['timezone'])
                    .where('id', '=', integration.user_id)
                    .executeTakeFirst();
                const assistantSettings = await this.db
                    .selectFrom('user_assistant_settings')
                    .select(['ai_humor', 'response_size'])
                    .where('user_id', '=', integration.user_id)
                    .executeTakeFirst();
                result = {
                    hasIntegration: true,
                    status: integration.status,
                    userId: integration.user_id,
                    timezone: user?.timezone || 'America/Sao_Paulo',
                    assistantSettings: assistantSettings ? {
                        ai_humor: assistantSettings.ai_humor,
                        response_size: assistantSettings.response_size
                    } : {
                        ai_humor: 'friendly',
                        response_size: 'medium'
                    }
                };
            }
            else {
                result = {
                    hasIntegration: false
                };
            }
            this.setCache(cacheKey, result);
            return result;
        }
        catch (error) {
            this.logger.error(`Erro ao verificar integração para telefone ${phone}:`, error);
            throw error;
        }
    }
    async getDashboard(phone) {
        try {
            const userId = await this.getUserIdByPhone(phone);
            const dashboardData = await this.dashboardService.getDashboardData(userId);
            return {
                user: dashboardData.user,
                tasks: dashboardData.tasks,
                finances: dashboardData.finances,
                ideas: dashboardData.ideas
            };
        }
        catch (error) {
            this.logger.error(`Erro ao buscar dashboard para telefone ${phone}:`, error);
            throw error;
        }
    }
    async createTask(createTaskDto) {
        const userId = await this.getUserIdByPhone(createTaskDto.phone);
        const userTimezone = await this.getUserTimezone(userId);
        const { phone, ...taskData } = createTaskDto;
        return this.tasksService.create(taskData, userId, userTimezone);
    }
    async findAllTasks(phone, page = 1, limit = 50) {
        const userId = await this.getUserIdByPhone(phone);
        const userTimezone = await this.getUserTimezone(userId);
        return this.tasksService.findAll(userId, userTimezone, page, limit);
    }
    async findOneTask(id, phone) {
        const userId = await this.getUserIdByPhone(phone);
        const userTimezone = await this.getUserTimezone(userId);
        return this.tasksService.findOne(id, userId, userTimezone);
    }
    async updateTask(id, updateTaskDto) {
        const userId = await this.getUserIdByPhone(updateTaskDto.phone);
        const userTimezone = await this.getUserTimezone(userId);
        const { phone, ...taskData } = updateTaskDto;
        return this.tasksService.update(id, taskData, userId, userTimezone);
    }
    async removeTask(id, phone) {
        const userId = await this.getUserIdByPhone(phone);
        return this.tasksService.remove(id, userId);
    }
    async completeTask(id, phone) {
        const userId = await this.getUserIdByPhone(phone);
        const userTimezone = await this.getUserTimezone(userId);
        return this.tasksService.complete(id, userId, userTimezone);
    }
    async createTaskCategory(createCategoryDto) {
        const userId = await this.getUserIdByPhone(createCategoryDto.phone);
        const { phone, ...categoryData } = createCategoryDto;
        return this.tasksService.createCategory(categoryData, userId);
    }
    async findAllTaskCategories(phone) {
        const userId = await this.getUserIdByPhone(phone);
        return this.tasksService.findAllCategories(userId);
    }
    async createFinance(createFinanceDto) {
        const userId = await this.getUserIdByPhone(createFinanceDto.phone);
        const userTimezone = await this.getUserTimezone(userId);
        const { phone, ...financeData } = createFinanceDto;
        return this.financesService.create(financeData, userId, userTimezone);
    }
    async findAllFinances(phone, page = 1, limit = 50) {
        const userId = await this.getUserIdByPhone(phone);
        const userTimezone = await this.getUserTimezone(userId);
        const paginatedResult = await this.financesService.findAll(userId, userTimezone, page, limit);
        const allFinances = await this.db
            .selectFrom('finances')
            .select(['transaction_type', 'amount'])
            .where('user_id', '=', userId)
            .execute();
        const summary = financial_utils_1.FinancialUtils.calculateSummary(allFinances);
        return new common_response_dto_1.PaginatedFinancialResponseDto(paginatedResult.finances, paginatedResult.total, paginatedResult.page, paginatedResult.limit, summary);
    }
    async findOneFinance(id, phone) {
        const userId = await this.getUserIdByPhone(phone);
        const userTimezone = await this.getUserTimezone(userId);
        return this.financesService.findOne(id, userId, userTimezone);
    }
    async updateFinance(id, updateFinanceDto) {
        const userId = await this.getUserIdByPhone(updateFinanceDto.phone);
        const userTimezone = await this.getUserTimezone(userId);
        const { phone, ...financeData } = updateFinanceDto;
        return this.financesService.update(id, financeData, userId, userTimezone);
    }
    async removeFinance(id, phone) {
        const userId = await this.getUserIdByPhone(phone);
        return this.financesService.remove(id, userId);
    }
    async getFinanceSummary(phone, startDate, endDate) {
        const userId = await this.getUserIdByPhone(phone);
        const userTimezone = await this.getUserTimezone(userId);
        return this.financesService.getSummary(userId, userTimezone, startDate, endDate);
    }
    async createFinanceCategory(createCategoryDto) {
        const userId = await this.getUserIdByPhone(createCategoryDto.phone);
        const { phone, ...categoryData } = createCategoryDto;
        return this.financesService.createCategory(categoryData, userId);
    }
    async findAllFinanceCategories(phone) {
        const userId = await this.getUserIdByPhone(phone);
        return this.financesService.findAllCategories(userId);
    }
    async createIdea(createIdeaDto) {
        const userId = await this.getUserIdByPhone(createIdeaDto.phone);
        const { phone, ...ideaData } = createIdeaDto;
        return this.ideasService.create(ideaData, userId);
    }
    async findAllIdeas(phone, page = 1, limit = 50) {
        const userId = await this.getUserIdByPhone(phone);
        const userTimezone = await this.getUserTimezone(userId);
        return this.ideasService.findAll(userId, userTimezone, page, limit);
    }
    async findOneIdea(id, phone) {
        const userId = await this.getUserIdByPhone(phone);
        const userTimezone = await this.getUserTimezone(userId);
        return this.ideasService.findOne(id, userId, userTimezone);
    }
    async updateIdea(id, updateIdeaDto) {
        const userId = await this.getUserIdByPhone(updateIdeaDto.phone);
        const userTimezone = await this.getUserTimezone(userId);
        const { phone, ...ideaData } = updateIdeaDto;
        return this.ideasService.update(id, ideaData, userId, userTimezone);
    }
    async removeIdea(id, phone) {
        const userId = await this.getUserIdByPhone(phone);
        return this.ideasService.remove(id, userId);
    }
    async toggleIdeaFavorite(id, phone) {
        const userId = await this.getUserIdByPhone(phone);
        const userTimezone = await this.getUserTimezone(userId);
        return this.ideasService.toggleFavorite(id, userId, userTimezone);
    }
    async createIdeaCategory(createCategoryDto) {
        const userId = await this.getUserIdByPhone(createCategoryDto.phone);
        const { phone, ...categoryData } = createCategoryDto;
        return this.ideasService.createCategory(categoryData, userId);
    }
    async findAllIdeaCategories(phone) {
        const userId = await this.getUserIdByPhone(phone);
        return this.ideasService.findAllCategories(userId);
    }
    async getRecentFinances(phone, limit = 10, days = 1) {
        const userId = await this.getUserIdByPhone(phone);
        const userTimezone = await this.getUserTimezone(userId);
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const recentTransactions = await this.db
            .selectFrom('finances')
            .leftJoin('finances_categories', 'finances.category_id', 'finances_categories.id')
            .select([
            'finances.id',
            'finances.transaction_type',
            'finances.amount',
            'finances.description',
            'finances.transaction_date',
            'finances_categories.name as category_name'
        ])
            .where('finances.user_id', '=', userId)
            .orderBy('finances.transaction_date', 'desc')
            .limit(limit)
            .execute();
        const totalCount = await this.db
            .selectFrom('finances')
            .select(this.db.fn.count('id').as('count'))
            .where('user_id', '=', userId)
            .where('transaction_date', '>=', startDate)
            .where('transaction_date', '<=', endDate)
            .executeTakeFirst();
        return {
            recent_transactions: recentTransactions.map(t => ({
                id: t.id,
                type: t.transaction_type,
                amount: t.amount,
                description: t.description,
                category_name: t.category_name,
                date: t.transaction_date
            })),
            total_in_period: Number(totalCount?.count || 0),
            period_days: days
        };
    }
    async getRecentTasks(phone, limit = 10, days = 1) {
        const userId = await this.getUserIdByPhone(phone);
        const userTimezone = await this.getUserTimezone(userId);
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const recentTasks = await this.db
            .selectFrom('tasks')
            .leftJoin('tasks_categories', 'tasks.category_id', 'tasks_categories.id')
            .select([
            'tasks.id',
            'tasks.task_type',
            'tasks.name',
            'tasks.description',
            'tasks.task_date',
            'tasks.completed_at',
            'tasks_categories.name as category_name'
        ])
            .where('tasks.user_id', '=', userId)
            .orderBy('tasks.created_at', 'desc')
            .limit(limit)
            .execute();
        const totalCount = await this.db
            .selectFrom('tasks')
            .select(this.db.fn.count('id').as('count'))
            .where('user_id', '=', userId)
            .where('created_at', '>=', startDate)
            .where('created_at', '<=', endDate)
            .executeTakeFirst();
        return {
            recent_tasks: recentTasks.map(t => ({
                id: t.id,
                type: t.task_type,
                name: t.name,
                description: t.description,
                task_date: t.task_date,
                completed: !!t.completed_at,
                category_name: t.category_name
            })),
            total_in_period: Number(totalCount?.count || 0),
            period_days: days
        };
    }
    async getRecentIdeas(phone, limit = 10, days = 1) {
        const userId = await this.getUserIdByPhone(phone);
        const userTimezone = await this.getUserTimezone(userId);
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const recentIdeas = await this.db
            .selectFrom('ideas')
            .leftJoin('ideas_categories', 'ideas.category_id', 'ideas_categories.id')
            .select([
            'ideas.id',
            'ideas.name',
            'ideas.description',
            'ideas.is_favorite',
            'ideas_categories.name as category_name'
        ])
            .where('ideas.user_id', '=', userId)
            .orderBy('ideas.created_at', 'desc')
            .limit(limit)
            .execute();
        const totalCount = await this.db
            .selectFrom('ideas')
            .select(this.db.fn.count('id').as('count'))
            .where('user_id', '=', userId)
            .where('created_at', '>=', startDate)
            .where('created_at', '<=', endDate)
            .executeTakeFirst();
        return {
            recent_ideas: recentIdeas.map(i => ({
                id: i.id,
                name: i.name,
                description: i.description,
                is_favorite: i.is_favorite,
                category_name: i.category_name
            })),
            total_in_period: Number(totalCount?.count || 0),
            period_days: days
        };
    }
    async createQuickTask(createTaskDto) {
        const userId = await this.getUserIdByPhone(createTaskDto.phone);
        const userTimezone = await this.getUserTimezone(userId);
        const taskData = {
            task_type: 'task',
            name: createTaskDto.name,
            description: createTaskDto.description
        };
        return this.tasksService.create(taskData, userId, userTimezone);
    }
    async validateNumber(validateDto) {
        try {
            this.logger.log(`Validando número ${validateDto.phone} com código ${validateDto.activation_code}`);
            const normalizedPhone = this.validateAndNormalizePhone(validateDto.phone);
            if (!validateDto.activation_code || validateDto.activation_code.trim().length === 0) {
                throw new common_1.BadRequestException('Código de ativação é obrigatório');
            }
            const integration = await this.db
                .selectFrom('integrations_whatsapp')
                .where('activation_code', '=', validateDto.activation_code.trim())
                .where('status', '=', 'pending')
                .selectAll()
                .executeTakeFirst();
            if (!integration) {
                throw new common_1.NotFoundException('Código de ativação inválido ou expirado');
            }
            const existingPhoneIntegration = await this.db
                .selectFrom('integrations_whatsapp')
                .where('phone', '=', normalizedPhone)
                .where('status', 'in', ['pending', 'active'])
                .where('id', '!=', integration.id)
                .executeTakeFirst();
            if (existingPhoneIntegration) {
                throw new common_1.BadRequestException('Este número de telefone já está sendo usado por outra integração');
            }
            await this.db
                .updateTable('integrations_whatsapp')
                .set({
                phone: normalizedPhone,
                status: 'active',
                is_validated: true,
                activation_code: null
            })
                .where('id', '=', integration.id)
                .execute();
            this.cache.delete(`user_phone_${normalizedPhone}`);
            this.cache.delete(`integration_check_${normalizedPhone}`);
            this.logger.log(`Número ${normalizedPhone} validado com sucesso`);
            return {
                success: true,
                message: 'Número validado com sucesso',
                phone: normalizedPhone,
                user_id: integration.user_id
            };
        }
        catch (error) {
            this.logger.error(`Erro ao validar número:`, error);
            throw error;
        }
    }
};
exports.AgentWppService = AgentWppService;
exports.AgentWppService = AgentWppService = AgentWppService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [tasks_service_1.TasksService,
        finances_service_1.FinancesService,
        ideas_service_1.IdeasService,
        dashboard_service_1.DashboardService])
], AgentWppService);
//# sourceMappingURL=agentwpp.service.js.map