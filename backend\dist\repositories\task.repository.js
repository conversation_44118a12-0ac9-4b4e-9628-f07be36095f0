"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskRepository = void 0;
const common_1 = require("@nestjs/common");
const kysely_1 = require("kysely");
const database_provider_1 = require("../database/database.provider");
const base_repository_1 = require("./base.repository");
const timezone_utils_1 = require("../common/utils/timezone.utils");
const error_utils_1 = require("../common/utils/error.utils");
let TaskRepository = class TaskRepository extends base_repository_1.BaseRepository {
    constructor(db) {
        super(db, 'TaskRepository');
    }
    get tableName() {
        return 'tasks';
    }
    get entityName() {
        return 'tarefa';
    }
    mapToResponseDto(entity, userTimezone = 'UTC') {
        return {
            id: entity.id,
            task_type: entity.task_type,
            category_id: entity.category_id || undefined,
            category_name: entity.category_name || undefined,
            name: entity.name,
            description: entity.description || undefined,
            task_date: entity.task_date ? timezone_utils_1.TimezoneUtils.toUserTimezone(entity.task_date, userTimezone) : undefined,
            user_id: entity.user_id,
            completed_at: entity.completed_at ? timezone_utils_1.TimezoneUtils.toUserTimezone(entity.completed_at, userTimezone) : undefined,
            created_at: timezone_utils_1.TimezoneUtils.toUserTimezone(entity.created_at, userTimezone),
            updated_at: timezone_utils_1.TimezoneUtils.toUserTimezone(entity.updated_at, userTimezone)
        };
    }
    prepareCreateData(dto, userId, userTimezone = 'UTC') {
        return {
            ...dto,
            user_id: userId,
            task_date: timezone_utils_1.TimezoneUtils.prepareDateForDatabase(dto.task_date, userTimezone),
            created_at: new Date(),
            updated_at: new Date()
        };
    }
    prepareUpdateData(dto, userTimezone = 'UTC') {
        const updateData = {
            ...dto,
            task_date: dto.task_date ?
                timezone_utils_1.TimezoneUtils.prepareDateForDatabase(dto.task_date, userTimezone) :
                undefined,
            updated_at: new Date()
        };
        Object.keys(updateData).forEach(key => {
            if (updateData[key] === undefined) {
                delete updateData[key];
            }
        });
        return updateData;
    }
    async findAllWithCategory(userId, userTimezone, page = 1, limit = 50, startDate, endDate) {
        try {
            const offset = (page - 1) * limit;
            let query = this.db
                .selectFrom('tasks')
                .leftJoin('tasks_categories', 'tasks.category_id', 'tasks_categories.id')
                .select([
                'tasks.id',
                'tasks.task_type',
                'tasks.category_id',
                'tasks_categories.name as category_name',
                'tasks.name',
                'tasks.description',
                'tasks.task_date',
                'tasks.user_id',
                'tasks.completed_at',
                'tasks.created_at',
                'tasks.updated_at'
            ])
                .where('tasks.user_id', '=', userId);
            if (startDate) {
                const startDateTime = timezone_utils_1.TimezoneUtils.prepareDateForDatabase(startDate, userTimezone);
                query = query.where('tasks.task_date', '>=', startDateTime);
            }
            if (endDate) {
                const endDateTime = timezone_utils_1.TimezoneUtils.prepareDateForDatabase(endDate, userTimezone);
                query = query.where('tasks.task_date', '<=', endDateTime);
            }
            const tasks = await query
                .orderBy('tasks.task_date', 'asc')
                .orderBy('tasks.created_at', 'desc')
                .limit(limit)
                .offset(offset)
                .execute();
            let countQuery = this.db
                .selectFrom('tasks')
                .select(this.db.fn.count('id').as('count'))
                .where('user_id', '=', userId);
            if (startDate) {
                const startDateTime = timezone_utils_1.TimezoneUtils.prepareDateForDatabase(startDate, userTimezone);
                countQuery = countQuery.where('task_date', '>=', startDateTime);
            }
            if (endDate) {
                const endDateTime = timezone_utils_1.TimezoneUtils.prepareDateForDatabase(endDate, userTimezone);
                countQuery = countQuery.where('task_date', '<=', endDateTime);
            }
            const total = await countQuery.executeTakeFirst();
            const data = tasks.map(task => this.mapToResponseDto(task, userTimezone));
            return {
                data,
                total: Number(total?.count || 0),
                page,
                limit
            };
        }
        catch (error) {
            error_utils_1.ErrorUtils.handleServiceError(this.logger, error, 'listar tarefas com categoria', userId);
        }
    }
    async complete(id, userId, userTimezone) {
        try {
            const task = await this.findOne(id, userId, userTimezone);
            const isCompleted = !!task.completed_at;
            await this.db
                .updateTable('tasks')
                .set({
                completed_at: isCompleted ? null : new Date(),
                updated_at: new Date()
            })
                .where('id', '=', id)
                .where('user_id', '=', userId)
                .execute();
            this.logger.log(`Tarefa ${id} ${isCompleted ? 'desmarcada como incompleta' : 'marcada como completa'} para usuário ${userId}`);
            return this.findOne(id, userId, userTimezone);
        }
        catch (error) {
            error_utils_1.ErrorUtils.handleServiceError(this.logger, error, 'alterar status da tarefa', userId);
        }
    }
};
exports.TaskRepository = TaskRepository;
exports.TaskRepository = TaskRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(database_provider_1.DATABASE_CONNECTION)),
    __metadata("design:paramtypes", [kysely_1.Kysely])
], TaskRepository);
//# sourceMappingURL=task.repository.js.map