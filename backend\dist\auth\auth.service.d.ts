import { JwtService } from '@nestjs/jwt';
import { RegisterDto } from './dto/register.dto';
export declare class AuthService {
    private readonly jwtService;
    private db;
    constructor(jwtService: JwtService);
    register(registerDto: RegisterDto): Promise<{
        status: string;
        data: never[];
    }>;
    validateUser(email: string, password: string): Promise<any>;
    login(email: string, password: string, deviceUuid?: string): Promise<{
        status: string;
        data: {
            access_token: string;
            refresh_token: string;
            device_uuid: string;
            user: {
                id: any;
                name: any;
                email: any;
                phone: any;
                timezone: any;
            };
        };
    }>;
    refresh(refreshToken: string, deviceUuid: string): Promise<{
        status: string;
        data: {
            access_token: string;
            refresh_token: string;
            device_uuid: string;
            user: {
                id: number;
                name: string;
                email: string;
                phone: string | null;
                timezone: string;
            };
        };
    }>;
    logout(refreshToken: string, deviceUuid: string): Promise<void>;
}
