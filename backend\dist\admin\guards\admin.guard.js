"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminGuard = void 0;
const common_1 = require("@nestjs/common");
const database_provider_1 = require("../../database/database.provider");
let AdminGuard = class AdminGuard {
    connection;
    constructor(connection) {
        this.connection = connection;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user || !user.sub) {
            throw new common_1.ForbiddenException('Usuário não autenticado');
        }
        const [rows] = await this.connection.execute('SELECT is_admin FROM users WHERE id = ? AND deleted_at IS NULL', [user.sub]);
        if (!Array.isArray(rows) || rows.length === 0) {
            throw new common_1.ForbiddenException('Usuário não encontrado');
        }
        const userData = rows[0];
        if (!userData.is_admin) {
            throw new common_1.ForbiddenException('Acesso negado. Apenas administradores podem acessar esta área');
        }
        return true;
    }
};
exports.AdminGuard = AdminGuard;
exports.AdminGuard = AdminGuard = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(database_provider_1.MYSQL2_CONNECTION)),
    __metadata("design:paramtypes", [Object])
], AdminGuard);
//# sourceMappingURL=admin.guard.js.map