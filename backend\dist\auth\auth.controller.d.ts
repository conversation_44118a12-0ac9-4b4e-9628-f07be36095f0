import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto): Promise<{
        status: string;
        data: never[];
    }>;
    login(loginDto: LoginDto): Promise<{
        status: string;
        data: {
            access_token: string;
            refresh_token: string;
            device_uuid: string;
            user: {
                id: any;
                name: any;
                email: any;
                phone: any;
                timezone: any;
            };
        };
    }>;
    refresh(body: {
        refresh_token: string;
        device_uuid: string;
    }): Promise<{
        status: string;
        data: {
            access_token: string;
            refresh_token: string;
            device_uuid: string;
            user: {
                id: number;
                name: string;
                email: string;
                phone: string | null;
                timezone: string;
            };
        };
    }>;
    logout(body: {
        refresh_token: string;
        device_uuid: string;
    }): Promise<{
        message: string;
    }>;
    protected(req: any): Promise<{
        message: string;
        user: any;
    }>;
}
