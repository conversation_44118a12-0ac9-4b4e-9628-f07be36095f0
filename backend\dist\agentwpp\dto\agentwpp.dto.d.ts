import { PaginationDto } from '../../common/dto/common-response.dto';
export declare class BaseAgentWppDto {
    phone: string;
}
export declare class AgentWppPaginationDto extends PaginationDto {
    search?: string;
    startDate?: string;
    endDate?: string;
    categoryId?: number;
}
export declare class CreateTaskAgentWppDto extends BaseAgentWppDto {
    task_type: 'appointment' | 'task';
    category_id?: number;
    name: string;
    description?: string;
    task_date?: string;
}
export declare class UpdateTaskAgentWppDto extends BaseAgentWppDto {
    task_type?: 'appointment' | 'task';
    category_id?: number;
    name?: string;
    description?: string;
    task_date?: string;
}
export declare class CreateTaskCategoryAgentWppDto extends BaseAgentWppDto {
    name: string;
}
export declare class CreateFinanceAgentWppDto extends BaseAgentWppDto {
    transaction_type: 'income' | 'expense';
    category_id?: number;
    is_saving?: boolean;
    description?: string;
    amount: string;
    transaction_date: string;
}
export declare class UpdateFinanceAgentWppDto extends BaseAgentWppDto {
    transaction_type?: 'income' | 'expense';
    category_id?: number;
    is_saving?: boolean;
    description?: string;
    amount?: string;
    transaction_date?: string;
}
export declare class CreateFinanceCategoryAgentWppDto extends BaseAgentWppDto {
    name: string;
    transaction_type: 'income' | 'expense';
    color?: string;
}
export declare class CreateIdeaAgentWppDto extends BaseAgentWppDto {
    category_id?: number;
    name: string;
    description?: string;
    content?: string;
    is_favorite?: boolean;
}
export declare class UpdateIdeaAgentWppDto extends BaseAgentWppDto {
    category_id?: number;
    name?: string;
    description?: string;
    content?: string;
    is_favorite?: boolean;
}
export declare class CreateIdeaCategoryAgentWppDto extends BaseAgentWppDto {
    name: string;
}
export declare class CheckIntegrationResponseDto {
    hasIntegration: boolean;
    status?: 'pending' | 'active' | 'inactive';
    userId?: number;
    timezone?: string;
    assistantSettings?: {
        ai_humor: string;
        response_size: string;
    };
}
export declare class AgentWppDashboardResponseDto {
    user: {
        name: string;
        timezone: string;
    };
    tasks: {
        completed: number;
        total: number;
    };
    finances: {
        spent: number;
        budget: number;
        income: number;
        savings: number;
    };
    ideas: {
        today: number;
        total: number;
        favorites: number;
    };
}
