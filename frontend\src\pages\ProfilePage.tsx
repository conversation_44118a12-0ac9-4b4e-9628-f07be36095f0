import React, { useState } from 'react';
import { User, <PERSON>tings, CreditCard, Share2, Upload, Check, ExternalLink, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from '../utils/toast';
import { useUserConfig, useUpdateUserConfig, useAnnualSavingsGoal, useAssistantSettings, useUpdateAssistantSettings } from '../hooks/useConfig';
import { useAuth } from '../contexts/AuthContext';
import { useWhatsAppIntegration, useCreateWhatsAppIntegration, useUpdateWhatsAppIntegration, useDeleteWhatsAppIntegration, useValidateWhatsAppIntegration, useValidateWithActivationCode, useGetWhatsAppUrl } from '../hooks/useIntegrations';
import { Button, Input, Label, Select, Card, CardHeader, CardTitle, CardContent } from '../primitives';
import ChangePasswordModal from '../components/ChangePasswordModal';
import ProfileDebug from '../components/ProfileDebug';
import { authenticatedApi } from '../lib/api';

interface CustomizeSettings {
  aiMood: 'formal' | 'moderate' | 'casual';
  responseLength: 'short' | 'medium' | 'long';
  reminderTime: string;
  reminderDelay: '15' | '30' | '60' | '180' | '360' | '1440';
}

interface PlanData {
  type: 'monthly' | 'annual';
  status: 'active';
  nextBilling: string;
  paymentMethod: string;
  aiCredits: number;
}

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  status: 'connected' | 'disconnected' | 'coming_soon';
  connectedDetails?: string;
}

const ProfilePage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');

  // Fetch user config from API
  const { data: userConfig, isLoading: configLoading, error: configError } = useUserConfig();
  const { data: savingsGoal } = useAnnualSavingsGoal();
  const updateUserMutation = useUpdateUserConfig();

  // Assistant settings hooks
  const { data: assistantSettings, isLoading: assistantLoading, error: assistantError } = useAssistantSettings();
  const updateAssistantMutation = useUpdateAssistantSettings();

  // WhatsApp integration hooks
  const { data: whatsappIntegration, isLoading: whatsappLoading } = useWhatsAppIntegration();
  const createWhatsAppMutation = useCreateWhatsAppIntegration();
  const updateWhatsAppMutation = useUpdateWhatsAppIntegration();
  const deleteWhatsAppMutation = useDeleteWhatsAppIntegration();
  const validateWhatsAppMutation = useValidateWhatsAppIntegration();
  const validateWithCodeMutation = useValidateWithActivationCode();
  const getWhatsAppUrlMutation = useGetWhatsAppUrl();
  
  const [whatsappPhone, setWhatsappPhone] = useState('');
  const [showValidationModal, setShowValidationModal] = useState(false);
  const [validationPhone, setValidationPhone] = useState('');
  const [activationCode, setActivationCode] = useState('');
  const [whatsappUrl, setWhatsappUrl] = useState('');
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);
  const [showGoogleCalendarModal, setShowGoogleCalendarModal] = useState(false);
  const [googleCalendarEmail, setGoogleCalendarEmail] = useState('');
  const [isSavingGoogleCalendar, setIsSavingGoogleCalendar] = useState(false);

  // Local state for form data
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    timezone: ''
  });

  // Effect to load user data into form when userConfig is loaded
  React.useEffect(() => {
    if (userConfig) {
      setFormData({
        name: userConfig.name || '',
        email: userConfig.email || '',
        phone: userConfig.phone || '',
        timezone: userConfig.timezone || 'America/Sao_Paulo'
      });
    }
  }, [userConfig]);

  // Update assistant settings when loaded from API
  React.useEffect(() => {
    if (assistantSettings) {
      // Mapear os valores do backend para o frontend
      const moodMapping: Record<string, 'formal' | 'moderate' | 'casual'> = {
        'formal': 'formal',
        'friendly': 'moderate', // friendly => moderate no frontend
        'casual': 'casual',
        'professional': 'formal' // professional => formal no frontend
      };
      
      setCustomizeSettings({
        aiMood: moodMapping[assistantSettings.ai_humor] || 'moderate',
        responseLength: assistantSettings.response_size as 'short' | 'medium' | 'long',
        reminderTime: assistantSettings.reminder_time || '09:00',
        reminderDelay: assistantSettings.reminder_interval as '15' | '30' | '60' | '180' | '360' | '1440'
      });
    }
  }, [assistantSettings]);

  const [customizeSettings, setCustomizeSettings] = useState<CustomizeSettings>({
    aiMood: 'moderate',
    responseLength: 'medium',
    reminderTime: '09:00',
    reminderDelay: '30'
  });



  const [planData] = useState<PlanData>({
    type: 'monthly',
    status: 'active',
    nextBilling: '2025-06-25',
    paymentMethod: 'Cartão final 1234',
    aiCredits: 1500
  });

  // WhatsApp integration handlers

  const handleWhatsAppDisconnect = async () => {
    if (!whatsappIntegration) return;
    
    if (window.confirm('Deseja realmente desconectar o WhatsApp?')) {
      try {
        await deleteWhatsAppMutation.mutateAsync(whatsappIntegration.id);
        toast.success('WhatsApp desconectado com sucesso!');
      } catch (error: any) {
        toast.error(error.message || 'Erro ao desconectar WhatsApp');
      }
    }
  };

  const handleWhatsAppConnect = async () => {
    try {
      await createWhatsAppMutation.mutateAsync({});
      toast.success('Código de ativação gerado com sucesso!');
    } catch (error: any) {
      toast.error(error.message || 'Erro ao gerar código de ativação');
    }
  };

  const handleGetWhatsAppUrl = async () => {
    if (!whatsappIntegration) return;

    try {
      const data = await getWhatsAppUrlMutation.mutateAsync(whatsappIntegration.id);
      setActivationCode(data.activation_code);
      setWhatsappUrl(data.whatsapp_url);
      setShowValidationModal(true);
    } catch (error: any) {
      toast.error(error.message || 'Erro ao obter URL do WhatsApp');
    }
  };

  const handleValidateWithCode = async () => {
    try {
      await validateWithCodeMutation.mutateAsync({ activation_code: activationCode, phone: validationPhone });
      toast.success('Número validado com sucesso!');
      setShowValidationModal(false);
    } catch (error: any) {
      toast.error(error.message || 'Erro ao validar número');
    }
  };

  const handleWhatsAppValidate = async () => {
    if (!whatsappIntegration) return;
    
    try {
      await validateWhatsAppMutation.mutateAsync(whatsappIntegration.id);
      toast.success('WhatsApp validado com sucesso!');
    } catch (error: any) {
      toast.error(error.message || 'Erro ao validar WhatsApp');
    }
  };

  // Google Calendar handlers
  const handleGoogleCalendarSave = async () => {
    if (!googleCalendarEmail || !googleCalendarEmail.includes('@')) {
      toast.error('Por favor, insira um email válido');
      return;
    }

    setIsSavingGoogleCalendar(true);
    try {
      // Simular salvamento - TODO: implementar chamada real para API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Salvar no localStorage temporariamente
      localStorage.setItem('googleCalendarEmail', googleCalendarEmail);
      
      toast.success('Email do Google Calendar configurado com sucesso!');
      setShowGoogleCalendarModal(false);
      setGoogleCalendarEmail('');
    } catch (error) {
      toast.error('Erro ao configurar email do Google Calendar');
    } finally {
      setIsSavingGoogleCalendar(false);
    }
  };

  const handleOpenGoogleCalendarModal = () => {
    // Carregar email salvo do localStorage se existir
    const savedEmail = localStorage.getItem('googleCalendarEmail');
    if (savedEmail) {
      setGoogleCalendarEmail(savedEmail);
    } else {
      // Usar email do perfil como sugestão
      setGoogleCalendarEmail(formData.email || '');
    }
    setShowGoogleCalendarModal(true);
  };

  const [integrations] = useState<Integration[]>([
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      description: 'Principal canal de comunicação',
      icon: <div className="w-6 h-6 text-[#25D366]" />,
      status: whatsappIntegration ? 'connected' : 'disconnected',
      connectedDetails: whatsappIntegration?.phone
    },
    {
      id: 'google-calendar',
      name: 'Google Agenda',
      description: 'Sincronize seus compromissos',
      icon: <div className="w-6 h-6 text-[#4285F4]" />,
      status: 'disconnected'
    },
    {
      id: 'google-sheets',
      name: 'Google Sheets',
      description: 'Exporte seus dados',
      icon: <div className="w-6 h-6 text-[#0F9D58]" />,
      status: 'disconnected'
    }
  ]);

  const tabs = [
    { id: 'profile', icon: User, label: 'Perfil' },
    { id: 'customize', icon: Settings, label: 'Assistente' },
    { id: 'plan', icon: CreditCard, label: 'Plano' },
    { id: 'integrations', icon: Share2, label: 'Integrações' }
  ];

  const handleSaveProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await updateUserMutation.mutateAsync(formData);
      toast.success('Informações pessoais salvas com sucesso!');
    } catch (error) {
      // Error handling is done by the mutation hook
      toast.error('Erro ao salvar informações pessoais');
    }
  };

  const handleSaveCustomizeSettings = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Mapeamento dos valores do frontend para o backend
      const backendMoodMapping: Record<string, string> = {
        'formal': 'formal',
        'moderate': 'friendly', // moderate do frontend => friendly do backend
        'casual': 'casual'
      };

      const payload = {
        ai_humor: backendMoodMapping[customizeSettings.aiMood] || 'friendly',
        response_size: customizeSettings.responseLength,
        reminder_time: customizeSettings.reminderTime,
        reminder_interval: customizeSettings.reminderDelay
      };

      await updateAssistantMutation.mutateAsync(payload);
      toast.success('Configurações do assistente salvas com sucesso!');
    } catch (error: any) {
      // Error handling is done by the mutation hook
      toast.error('Erro ao salvar configurações do assistente');
    }
  };

  // Loading state
  if (configLoading) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando configurações...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (configError) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Erro ao carregar configurações</h2>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar suas configurações. Tente novamente.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F7F7F7] pb-24">
      {/* Profile Card */}
      <div className="bg-white">
        <div className="max-w-2xl mx-auto px-4 py-6">
          <div className="flex items-center gap-4">
            <div className="relative">
              <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden">
                <span className="text-2xl font-semibold text-gray-900">
                  {(user?.name || formData.name || 'U')[0].toUpperCase()}
                </span>
              </div>
              <button className="absolute bottom-0 right-0 p-1 bg-[#B4EB00] rounded-full">
                <Upload size={14} className="text-gray-900" />
              </button>
            </div>
            <div className="flex-1 min-w-0">
              <h1 className="text-xl font-semibold text-gray-900 truncate">
                {user?.name || formData.name || 'Usuário'}
              </h1>
              <p className="text-gray-600 truncate">
                {user?.email || formData.email || '<EMAIL>'}
              </p>
              <div className="inline-flex items-center gap-1 px-2 py-1 bg-[#B4EB00]/10 rounded-full mt-2">
                <Check size={14} className="text-[#B4EB00]" />
                <span className="text-xs font-medium text-gray-900">Ativo</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-white border-t border-b border-gray-100">
        <div className="max-w-2xl mx-auto px-4">
          <div className="flex justify-between">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex flex-col items-center py-3 px-4 transition-colors relative ${
                  activeTab === tab.id ? 'text-[#B4EB00]' : 'text-gray-600'
                }`}
              >
                <tab.icon size={20} />
                <span className="text-xs mt-1">{tab.label}</span>
                {activeTab === tab.id && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#B4EB00]"
                  />
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-2xl mx-auto px-4 py-6">
        <AnimatePresence mode="wait">
          {activeTab === 'profile' && (
            <motion.div
              key="profile"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <Card>
                <CardHeader>
                  <CardTitle>Informações Pessoais</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSaveProfile} className="space-y-4">
                    <div>
                      <Label>Nome</Label>
                      <Input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label>Email</Label>
                      <Input
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label>Telefone</Label>
                      <Input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label>Timezone</Label>
                      <Select
                        value={formData.timezone}
                        onChange={(e) => setFormData({ ...formData, timezone: e.target.value })}
                      >
                        <optgroup label="Brasil">
                          <option value="America/Sao_Paulo">São Paulo (GMT-3)</option>
                          <option value="America/Manaus">Manaus (GMT-4)</option>
                          <option value="America/Rio_Branco">Rio Branco (GMT-5)</option>
                          <option value="America/Belem">Belém (GMT-3)</option>
                          <option value="America/Fortaleza">Fortaleza (GMT-3)</option>
                          <option value="America/Recife">Recife (GMT-3)</option>
                          <option value="America/Salvador">Salvador (GMT-3)</option>
                          <option value="America/Campo_Grande">Campo Grande (GMT-4)</option>
                          <option value="America/Cuiaba">Cuiabá (GMT-4)</option>
                          <option value="America/Porto_Velho">Porto Velho (GMT-4)</option>
                          <option value="America/Boa_Vista">Boa Vista (GMT-4)</option>
                          <option value="America/Noronha">Fernando de Noronha (GMT-2)</option>
                        </optgroup>
                        <optgroup label="América do Norte">
                          <option value="America/New_York">Nova York (GMT-5)</option>
                          <option value="America/Los_Angeles">Los Angeles (GMT-8)</option>
                          <option value="America/Chicago">Chicago (GMT-6)</option>
                          <option value="America/Denver">Denver (GMT-7)</option>
                          <option value="America/Toronto">Toronto (GMT-5)</option>
                          <option value="America/Vancouver">Vancouver (GMT-8)</option>
                          <option value="America/Mexico_City">Cidade do México (GMT-6)</option>
                        </optgroup>
                        <optgroup label="América do Sul">
                          <option value="America/Argentina/Buenos_Aires">Buenos Aires (GMT-3)</option>
                          <option value="America/Santiago">Santiago (GMT-3)</option>
                          <option value="America/Lima">Lima (GMT-5)</option>
                          <option value="America/Bogota">Bogotá (GMT-5)</option>
                          <option value="America/Caracas">Caracas (GMT-4)</option>
                          <option value="America/Montevideo">Montevidéu (GMT-3)</option>
                          <option value="America/La_Paz">La Paz (GMT-4)</option>
                        </optgroup>
                        <optgroup label="Europa">
                          <option value="Europe/London">Londres (GMT+0)</option>
                          <option value="Europe/Paris">Paris (GMT+1)</option>
                          <option value="Europe/Berlin">Berlim (GMT+1)</option>
                          <option value="Europe/Madrid">Madrid (GMT+1)</option>
                          <option value="Europe/Rome">Roma (GMT+1)</option>
                          <option value="Europe/Amsterdam">Amsterdam (GMT+1)</option>
                          <option value="Europe/Zurich">Zurique (GMT+1)</option>
                          <option value="Europe/Moscow">Moscou (GMT+3)</option>
                          <option value="Europe/Lisbon">Lisboa (GMT+0)</option>
                        </optgroup>
                        <optgroup label="Ásia">
                          <option value="Asia/Tokyo">Tóquio (GMT+9)</option>
                          <option value="Asia/Shanghai">Xangai (GMT+8)</option>
                          <option value="Asia/Seoul">Seul (GMT+9)</option>
                          <option value="Asia/Singapore">Singapura (GMT+8)</option>
                          <option value="Asia/Dubai">Dubai (GMT+4)</option>
                          <option value="Asia/Kolkata">Mumbai (GMT+5:30)</option>
                          <option value="Asia/Bangkok">Bangkok (GMT+7)</option>
                          <option value="Asia/Hong_Kong">Hong Kong (GMT+8)</option>
                        </optgroup>
                        <optgroup label="Oceania">
                          <option value="Australia/Sydney">Sydney (GMT+10)</option>
                          <option value="Australia/Melbourne">Melbourne (GMT+10)</option>
                          <option value="Australia/Perth">Perth (GMT+8)</option>
                          <option value="Pacific/Auckland">Auckland (GMT+12)</option>
                        </optgroup>
                        <optgroup label="África">
                          <option value="Africa/Cairo">Cairo (GMT+2)</option>
                          <option value="Africa/Johannesburg">Joanesburgo (GMT+2)</option>
                          <option value="Africa/Lagos">Lagos (GMT+1)</option>
                          <option value="Africa/Casablanca">Casablanca (GMT+0)</option>
                        </optgroup>
                      </Select>
                    </div>
                    <Button
                      type="submit"
                      variant="primary"
                      size="lg"
                      className="w-full"
                      disabled={updateUserMutation.isPending}
                    >
                      {updateUserMutation.isPending ? 'Salvando...' : 'Salvar Alterações'}
                    </Button>
                  </form>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Segurança</CardTitle>
                </CardHeader>
                <CardContent>
                  <Button 
                    variant="secondary" 
                    size="lg" 
                    className="w-full"
                    onClick={() => setShowChangePasswordModal(true)}
                  >
                    Alterar Senha
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {activeTab === 'customize' && (
            <motion.div
              key="customize"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-semibold text-gray-900">Personalizar Assistente</h2>
                  <span className="px-2 py-1 bg-[#B4EB00]/10 rounded-full text-xs font-medium text-gray-900">Beta</span>
                </div>
                <form onSubmit={handleSaveCustomizeSettings} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Humor da IA</label>
                    <select
                      value={customizeSettings.aiMood}
                      onChange={(e) => setCustomizeSettings({ ...customizeSettings, aiMood: e.target.value as any })}
                      className="w-full h-12 px-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                    >
                      <option value="formal">Formal</option>
                      <option value="moderate">Moderado</option>
                      <option value="casual">Descontraído</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Tamanho das Respostas</label>
                    <select
                      value={customizeSettings.responseLength}
                      onChange={(e) => setCustomizeSettings({ ...customizeSettings, responseLength: e.target.value as any })}
                      className="w-full h-12 px-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                    >
                      <option value="short">Curtas</option>
                      <option value="medium">Médias</option>
                      <option value="long">Longas</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Horário de Lembretes</label>
                    <input
                      type="time"
                      value={customizeSettings.reminderTime}
                      onChange={(e) => setCustomizeSettings({ ...customizeSettings, reminderTime: e.target.value })}
                      className="w-full h-12 px-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Intervalo entre Lembretes</label>
                    <select
                      value={customizeSettings.reminderDelay}
                      onChange={(e) => setCustomizeSettings({ ...customizeSettings, reminderDelay: e.target.value as any })}
                      className="w-full h-12 px-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                    >
                      <option value="15">15 minutos</option>
                      <option value="30">30 minutos</option>
                      <option value="60">1 hora</option>
                      <option value="180">3 horas</option>
                      <option value="360">6 horas</option>
                      <option value="1440">1 dia</option>
                    </select>
                  </div>
                  <button
                    type="submit"
                    disabled={updateAssistantMutation.isPending}
                    className="w-full h-12 bg-[#B4EB00] text-gray-900 rounded-xl font-medium hover:bg-opacity-90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {updateAssistantMutation.isPending ? 'Salvando...' : 'Salvar Preferências'}
                  </button>
                </form>
              </div>
            </motion.div>
          )}

          {activeTab === 'plan' && (
            <motion.div
              key="plan"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Plano Atual</h2>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Plano</span>
                    <span className="font-medium text-gray-900">Premium Mensal</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Próxima cobrança</span>
                    <span className="font-medium text-gray-900">25/06/2025</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Método de pagamento</span>
                    <span className="font-medium text-gray-900">{planData.paymentMethod}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Créditos de IA</span>
                    <span className="font-medium text-gray-900">{planData.aiCredits}</span>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Comprar Créditos</h2>
                <button
                  className="w-full h-12 bg-[#B4EB00] text-gray-900 rounded-xl font-medium hover:bg-opacity-90 transition-colors"
                >
                  Adicionar Créditos
                </button>
              </div>
            </motion.div>
          )}

          {activeTab === 'integrations' && (
            <motion.div
              key="integrations"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              {/* WhatsApp Integration */}
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-xl bg-[#25D366]/10 flex items-center justify-center">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="text-[#25D366]">
                      <path d="M20.52 3.48A11.87 11.87 0 0 0 12 0C5.37 0 0 5.37 0 12c0 2.1.55 4.16 1.59 6L0 24l6.14-1.61A11.87 11.87 0 0 0 12 24c6.63 0 12-5.37 12-12 0-3.2-1.24-6.21-3.48-8.52zM12 21.75A9.75 9.75 0 0 1 6.23 20l-.44-.26-4.56 1.2L2.43 16.5A9.75 9.75 0 1 1 12 21.75zm5.34-7.3c-.29-.15-1.73-.85-2-.95s-.46-.15-.66.15-.76.95-.93 1.15-.34.23-.63.08a7.93 7.93 0 0 1-2.34-1.44 8.8 8.8 0 0 1-1.62-2.01c-.17-.29 0-.45.13-.59s.29-.34.44-.51a2 2 0 0 0 .29-.48.54.54 0 0 0 0-.51c-.07-.15-.66-1.58-.9-2.17s-.47-.49-.66-.5h-.57a1.1 1.1 0 0 0-.8.37 3.36 3.36 0 0 0-1.05 2.5 5.82 5.82 0 0 0 1.22 3.11c.15.2 2.1 3.2 5.08 4.49.71.31 1.26.49 1.69.63a4.06 4.06 0 0 0 1.86.12 3 3 0 0 0 2-1.4 2.48 2.48 0 0 0 .17-1.4c-.08-.15-.29-.23-.58-.37z" fill="currentColor"/>
                    </svg>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold text-gray-900">WhatsApp</h3>
                      {whatsappIntegration && (
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          whatsappIntegration.status === 'active' 
                            ? 'bg-[#25D366]/10 text-[#25D366]'
                            : whatsappIntegration.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          {whatsappIntegration.status === 'active' ? 'Conectado' 
                           : whatsappIntegration.status === 'pending' ? 'Pendente'
                           : 'Inativo'}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      {whatsappIntegration?.status === 'pending'
                        ? 'Envie o código de ativação para validar seu número'
                        : 'Principal canal de comunicação'}
                    </p>
                    
                    {whatsappIntegration ? (
                      <>
                        {whatsappIntegration.phone && (
                          <p className="text-sm text-gray-500 mb-4">{whatsappIntegration.phone}</p>
                        )}
                        
                        {whatsappIntegration.status === 'pending' && whatsappIntegration.activation_code ? (
                          <div className="space-y-4">
                            <div className="bg-gray-50 rounded-xl p-4">
                              <h4 className="font-medium text-gray-900 mb-2">Código de Ativação</h4>
                              <div className="flex items-center gap-2 p-3 bg-white border-2 border-dashed border-gray-200 rounded-lg">
                                <span className="font-mono text-lg text-gray-900 flex-1">{whatsappIntegration.activation_code}</span>
                                <button
                                  onClick={() => {
                                    navigator.clipboard.writeText(whatsappIntegration.activation_code || '');
                                    toast.success('Código copiado para a área de transferência!');
                                  }}
                                  className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-md text-sm font-medium transition-colors"
                                >
                                  Copiar
                                </button>
                              </div>
                              <p className="text-sm text-gray-600 mt-2">
                                Envie este código no WhatsApp para validar sua integração
                              </p>
                            </div>
                            
                            <div className="flex gap-2 flex-wrap">
                              <button
                                onClick={handleGetWhatsAppUrl}
                                disabled={getWhatsAppUrlMutation.isPending}
                                className="inline-flex items-center gap-2 h-10 px-4 bg-[#25D366] text-white rounded-xl hover:bg-[#25D366]/90 transition-colors disabled:opacity-50"
                              >
                                {getWhatsAppUrlMutation.isPending ? 'Carregando...' : 'Enviar via WhatsApp'}
                                <ExternalLink size={16} />
                              </button>
                              <button
                                onClick={handleWhatsAppDisconnect}
                                disabled={deleteWhatsAppMutation.isPending}
                                className="inline-flex items-center gap-2 h-10 px-4 text-red-600 hover:bg-red-50 rounded-xl transition-colors disabled:opacity-50"
                              >
                                {deleteWhatsAppMutation.isPending ? 'Removendo...' : 'Remover'}
                              </button>
                            </div>
                          </div>
                        ) : whatsappIntegration.status === 'active' ? (
                          <div className="flex gap-2 flex-wrap">
                            <button
                              onClick={handleWhatsAppDisconnect}
                              disabled={deleteWhatsAppMutation.isPending}
                              className="inline-flex items-center gap-2 h-10 px-4 text-red-600 hover:bg-red-50 rounded-xl transition-colors disabled:opacity-50"
                            >
                              {deleteWhatsAppMutation.isPending ? 'Removendo...' : 'Remover'}
                            </button>
                          </div>
                        ) : null}
                      </>
                    ) : (
                      <div className="space-y-4">
                        <p className="text-sm text-gray-600">
                          Gere um código de ativação e envie no WhatsApp para validar sua integração
                        </p>
                        <button
                          onClick={handleWhatsAppConnect}
                          disabled={createWhatsAppMutation.isPending}
                          className="inline-flex items-center gap-2 h-10 px-4 bg-[#B4EB00] text-gray-900 rounded-xl hover:bg-opacity-90 transition-colors disabled:opacity-50"
                        >
                          {createWhatsAppMutation.isPending ? 'Gerando Código...' : 'Gerar Código de Ativação'}
                          <ExternalLink size={16} />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Modal de Validação */}
              {showValidationModal && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
                  onClick={() => setShowValidationModal(false)}
                >
                  <motion.div
                    initial={{ scale: 0.95, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0.95, opacity: 0 }}
                    className="bg-white rounded-2xl p-6 w-full max-w-md"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">Validar Número</h2>
                    <p className="text-sm text-gray-600 mb-4">
                      Envie este código para a pessoa convidada e oriente-a a enviá-lo no WhatsApp.
                    </p>
                    
                    <div className="space-y-4">
                      <div className="bg-gray-50 rounded-xl p-4">
                        <div className="flex items-center gap-2 p-3 bg-white border-2 border-dashed border-gray-200 rounded-lg">
                          <span className="font-mono text-lg text-gray-900 flex-1">{activationCode}</span>
                          <button
                            onClick={() => {
                              navigator.clipboard.writeText(activationCode);
                              toast.success('Código copiado para a área de transferência!');
                            }}
                            className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-md text-sm font-medium transition-colors"
                          >
                            Copiar Código
                          </button>
                        </div>
                      </div>
                      
                      <div className="hidden">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Número que será validado
                        </label>
                        <input
                          type="tel"
                          value={validationPhone}
                          onChange={(e) => setValidationPhone(e.target.value)}
                          placeholder="+55 11 99999-9999"
                          className="w-full h-12 px-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                        />
                      </div>
                      
                      <div className="flex gap-2 pt-4">
                        <button
                          onClick={() => window.open(whatsappUrl, '_blank')}
                          className="flex-1 h-10 bg-[#25D366] text-white rounded-xl hover:bg-[#25D366]/90 transition-colors"
                        >
                          Enviar via WhatsApp
                        </button>
                        <button
                          onClick={handleValidateWithCode}
                          disabled={validateWithCodeMutation.isPending || !validationPhone.trim()}
                          className="hidden flex-1 h-10 bg-[#B4EB00] text-gray-900 rounded-xl hover:bg-opacity-90 transition-colors disabled:opacity-50"
                        >
                          {validateWithCodeMutation.isPending ? 'Validando...' : 'Validar'}
                        </button>
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              )}

              {/* Google Calendar Integration */}
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-xl bg-[#4285F4]/10 flex items-center justify-center">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="text-[#4285F4]">
                      <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z" fill="currentColor"/>
                    </svg>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold text-gray-900">Google Agenda</h3>
                      {localStorage.getItem('googleCalendarEmail') ? (
                        <span className="px-2 py-1 bg-green-100 rounded-full text-xs font-medium text-green-800">
                          Configurado
                        </span>
                      ) : (
                        <span className="px-2 py-1 bg-gray-100 rounded-full text-xs font-medium text-gray-600">
                          Não configurado
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      Configure o email para receber convites do Google Calendar
                    </p>
                    
                    {localStorage.getItem('googleCalendarEmail') && (
                      <p className="text-sm text-gray-500 mb-4">
                        Email: {localStorage.getItem('googleCalendarEmail')}
                      </p>
                    )}
                    
                    <button
                      onClick={handleOpenGoogleCalendarModal}
                      className="inline-flex items-center gap-2 h-10 px-4 bg-[#4285F4] text-white rounded-xl hover:bg-[#4285F4]/90 transition-colors"
                    >
                      <span>{localStorage.getItem('googleCalendarEmail') ? 'Alterar Email' : 'Configurar Email'}</span>
                      <ExternalLink size={16} />
                    </button>
                  </div>
                </div>
              </div>

              {/* Google Sheets Integration */}
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-xl bg-gray-100 flex items-center justify-center">
                    <div className="w-6 h-6 text-[#0F9D58]" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold text-gray-900">Google Sheets</h3>
                      <span className="px-2 py-1 bg-gray-100 rounded-full text-xs font-medium text-gray-600">
                        Em breve
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">Exporte seus dados</p>
                    <button
                      disabled
                      className="inline-flex items-center gap-2 h-10 px-4 bg-gray-100 text-gray-400 rounded-xl cursor-not-allowed"
                    >
                      <span>Em breve</span>
                      <ExternalLink size={16} />
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      {/* Change Password Modal */}
      <ChangePasswordModal 
        isOpen={showChangePasswordModal}
        onClose={() => setShowChangePasswordModal(false)}
      />

      {/* Google Calendar Modal */}
      {showGoogleCalendarModal && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={() => setShowGoogleCalendarModal(false)}
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className="bg-white rounded-2xl p-6 w-full max-w-md"
            onClick={(e) => e.stopPropagation()}
          >
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Configurar Google Calendar</h2>
            <p className="text-sm text-gray-600 mb-4">
              Digite o email que receberá os convites do Google Calendar enviados pela plataforma.
            </p>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email para receber convites
                </label>
                <input
                  type="email"
                  value={googleCalendarEmail}
                  onChange={(e) => setGoogleCalendarEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full h-12 px-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[#4285F4] focus:border-transparent"
                  required
                />
              </div>
              
              <div className="flex gap-3 pt-4">
                <button
                  onClick={() => setShowGoogleCalendarModal(false)}
                  className="flex-1 h-10 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleGoogleCalendarSave}
                  disabled={isSavingGoogleCalendar || !googleCalendarEmail.trim()}
                  className="flex-1 h-10 bg-[#4285F4] text-white rounded-xl hover:bg-[#4285F4]/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSavingGoogleCalendar ? 'Salvando...' : 'Salvar'}
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Debug Component - Remove in production */}
      {process.env.NODE_ENV === 'development' && <ProfileDebug />}
    </div>
  );
};

export default ProfilePage;
