import { Connection } from 'mysql2/promise';
import { AdminUserListResponseDto, AdminDashboardStatsDto, UpdatePlanDto, UpdatePaymentSettingsDto, UpdateUserDto } from './dto/admin.dto';
export declare class AdminService {
    private connection;
    constructor(connection: Connection);
    getDashboardStats(): Promise<AdminDashboardStatsDto>;
    getUsers(page?: number, limit?: number): Promise<{
        users: AdminUserListResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    updateUser(userId: number, updateDto: UpdateUserDto): Promise<void>;
    getPlans(): Promise<any[]>;
    updatePlan(planId: number, updateDto: UpdatePlanDto): Promise<void>;
    getPaymentSettings(): Promise<{
        id: any;
        stripe_public_key: any;
        stripe_secret_key: string | null;
        stripe_webhook_secret: string | null;
        is_active: boolean;
        created_at: any;
        updated_at: any;
    } | null>;
    updatePaymentSettings(updateDto: UpdatePaymentSettingsDto): Promise<void>;
    getStripeConfig(): Promise<{
        public_key: any;
        secret_key: string | null;
        webhook_endpoint_secret: string | null;
        test_mode: boolean;
    } | null>;
    saveStripeConfig(config: {
        public_key: string;
        secret_key: string;
        webhook_endpoint_secret?: string;
        test_mode: boolean;
    }): Promise<void>;
    testStripeConnection(config: {
        secret_key: string;
        test_mode: boolean;
    }): Promise<{
        message: string;
    }>;
}
