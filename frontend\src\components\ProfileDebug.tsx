import React from 'react';
import { useUserConfig, useAssistantSettings } from '../hooks/useConfig';
import { useWhatsAppIntegration } from '../hooks/useIntegrations';
import { useAuth } from '../contexts/AuthContext';

const ProfileDebug: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { data: userConfig, isLoading: configLoading, error: configError } = useUserConfig();
  const { data: assistantSettings, isLoading: assistantLoading, error: assistantError } = useAssistantSettings();
  const { data: whatsappIntegration, isLoading: whatsappLoading, error: whatsappError } = useWhatsAppIntegration();

  return (
    <div className="fixed top-4 right-4 bg-white p-4 rounded-lg shadow-lg border max-w-md z-50">
      <h3 className="font-bold mb-2">Debug Profile Data</h3>
      
      <div className="space-y-2 text-sm">
        <div>
          <strong>Auth Status:</strong> {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
        </div>
        
        <div>
          <strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'null'}
        </div>
        
        <div>
          <strong>User Config:</strong>
          <div>Loading: {configLoading ? 'true' : 'false'}</div>
          <div>Error: {configError ? JSON.stringify(configError) : 'none'}</div>
          <div>Data: {userConfig ? JSON.stringify(userConfig, null, 2) : 'null'}</div>
        </div>
        
        <div>
          <strong>Assistant Settings:</strong>
          <div>Loading: {assistantLoading ? 'true' : 'false'}</div>
          <div>Error: {assistantError ? JSON.stringify(assistantError) : 'none'}</div>
          <div>Data: {assistantSettings ? JSON.stringify(assistantSettings, null, 2) : 'null'}</div>
        </div>
        
        <div>
          <strong>WhatsApp Integration:</strong>
          <div>Loading: {whatsappLoading ? 'true' : 'false'}</div>
          <div>Error: {whatsappError ? JSON.stringify(whatsappError) : 'none'}</div>
          <div>Data: {whatsappIntegration ? JSON.stringify(whatsappIntegration, null, 2) : 'null'}</div>
        </div>
      </div>
    </div>
  );
};

export default ProfileDebug;
