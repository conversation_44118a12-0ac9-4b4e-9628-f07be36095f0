import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { authenticatedApi } from '../lib/api';
import {
  UserConfigResponseDto,
  UpdateUserConfigDto,
  AnnualSavingsGoalResponseDto,
  CreateAnnualSavingsGoalDto,
  UpdateAnnualSavingsGoalDto,
  AssistantSettingsResponseDto,
  UpdateAssistantSettingsDto,
} from '../types/api';

// Hook para buscar configurações do usuário
export const useUserConfig = () => {
  return useQuery({
    queryKey: ['user-config'],
    queryFn: async (): Promise<UserConfigResponseDto> => {
      const response = await authenticatedApi.get('profile');
      return response.json();
    },
    staleTime: 10 * 60 * 1000, // 10 minutos
  });
};

// Hook para atualizar configurações do usuário
export const useUpdateUserConfig = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateUserConfigDto): Promise<UserConfigResponseDto> => {
      const response = await authenticatedApi.put('profile/personal-info', { json: data });
      return response.json();
    },
    onSuccess: (data) => {
      // Atualizar cache das configurações do usuário
      queryClient.setQueryData(['user-config'], data);
      // Invalidar dashboard para refletir mudanças no nome/timezone
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });
};

// Hook para buscar meta anual de economia
export const useAnnualSavingsGoal = (year?: number) => {
  const currentYear = year || new Date().getFullYear();
  
  return useQuery({
    queryKey: ['annual-savings-goal', currentYear],
    queryFn: async (): Promise<AnnualSavingsGoalResponseDto | null> => {
      try {
        const response = await authenticatedApi.get('config/annual-savings-goal');
        return response.json();
      } catch (error: any) {
        // Se não encontrar meta (404), retornar null
        if (error?.response?.status === 404) {
          return null;
        }
        throw error;
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutos
  });
};

// Hook para criar meta anual de economia
export const useCreateAnnualSavingsGoal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateAnnualSavingsGoalDto): Promise<AnnualSavingsGoalResponseDto> => {
      const response = await authenticatedApi.post('config/annual-savings-goal', { json: data });
      return response.json();
    },
    onSuccess: (data) => {
      // Atualizar cache da meta anual
      queryClient.setQueryData(['annual-savings-goal', data.year], data);
      // Invalidar dashboard para refletir nova meta
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });
};

// Hook para atualizar meta anual de economia
export const useUpdateAnnualSavingsGoal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateAnnualSavingsGoalDto): Promise<AnnualSavingsGoalResponseDto> => {
      const response = await authenticatedApi.post('config/annual-savings-goal', { json: data });
      return response.json();
    },
    onSuccess: (data) => {
      // Atualizar cache da meta anual
      queryClient.setQueryData(['annual-savings-goal', data.year], data);
      // Invalidar dashboard para refletir mudança na meta
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });
};

// Hook para buscar configurações do assistente
export const useAssistantSettings = () => {
  return useQuery({
    queryKey: ['assistant-settings'],
    queryFn: async (): Promise<AssistantSettingsResponseDto> => {
      const response = await authenticatedApi.get('profile/assistant-settings');
      return response.json();
    },
    staleTime: 10 * 60 * 1000, // 10 minutos
  });
};

// Hook para atualizar configurações do assistente
export const useUpdateAssistantSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateAssistantSettingsDto): Promise<any> => {
      const response = await authenticatedApi.put('profile/assistant-settings', { json: data });
      return response.json();
    },
    onSuccess: () => {
      // Invalidar cache das configurações do assistente para recarregar dados atualizados
      queryClient.invalidateQueries({ queryKey: ['assistant-settings'] });
    },
  });
};
