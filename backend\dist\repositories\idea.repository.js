"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdeaRepository = void 0;
const common_1 = require("@nestjs/common");
const kysely_1 = require("kysely");
const database_provider_1 = require("../database/database.provider");
const base_repository_1 = require("./base.repository");
const timezone_utils_1 = require("../common/utils/timezone.utils");
const error_utils_1 = require("../common/utils/error.utils");
let IdeaRepository = class IdeaRepository extends base_repository_1.BaseRepository {
    constructor(db) {
        super(db, 'IdeaRepository');
    }
    get tableName() {
        return 'ideas';
    }
    get entityName() {
        return 'ideia';
    }
    mapToResponseDto(entity, userTimezone = 'UTC') {
        return {
            id: entity.id,
            category_id: entity.category_id || undefined,
            category_name: entity.category_name || undefined,
            name: entity.name,
            description: entity.description || undefined,
            content: entity.content || undefined,
            is_favorite: entity.is_favorite || undefined,
            user_id: entity.user_id,
            created_at: timezone_utils_1.TimezoneUtils.toUserTimezone(entity.created_at, userTimezone),
            updated_at: timezone_utils_1.TimezoneUtils.toUserTimezone(entity.updated_at, userTimezone)
        };
    }
    prepareCreateData(dto, userId) {
        return {
            ...dto,
            user_id: userId,
            created_at: new Date(),
            updated_at: new Date()
        };
    }
    prepareUpdateData(dto) {
        return {
            ...dto,
            updated_at: new Date()
        };
    }
    async findAllWithCategory(userId, userTimezone, page = 1, limit = 50) {
        try {
            const offset = (page - 1) * limit;
            const ideas = await this.db
                .selectFrom('ideas')
                .leftJoin('ideas_categories', 'ideas.category_id', 'ideas_categories.id')
                .select([
                'ideas.id',
                'ideas.category_id',
                'ideas_categories.name as category_name',
                'ideas.name',
                'ideas.description',
                'ideas.content',
                'ideas.is_favorite',
                'ideas.user_id',
                'ideas.created_at',
                'ideas.updated_at'
            ])
                .where('ideas.user_id', '=', userId)
                .orderBy('ideas.created_at', 'desc')
                .limit(limit)
                .offset(offset)
                .execute();
            const total = await this.db
                .selectFrom('ideas')
                .select(this.db.fn.count('id').as('count'))
                .where('user_id', '=', userId)
                .executeTakeFirst();
            const data = ideas.map(idea => this.mapToResponseDto(idea, userTimezone));
            return {
                data,
                total: Number(total?.count || 0),
                page,
                limit
            };
        }
        catch (error) {
            error_utils_1.ErrorUtils.handleServiceError(this.logger, error, 'listar ideias com categoria', userId);
        }
    }
    async toggleFavorite(id, userId) {
        try {
            const idea = await this.findOne(id, userId);
            const newFavoriteStatus = !idea.is_favorite;
            await this.db
                .updateTable('ideas')
                .set({
                is_favorite: newFavoriteStatus,
                updated_at: new Date()
            })
                .where('id', '=', id)
                .where('user_id', '=', userId)
                .execute();
            this.logger.log(`Ideia ${id} ${newFavoriteStatus ? 'marcada como favorita' : 'desmarcada como favorita'} para usuário ${userId}`);
            return this.findOne(id, userId);
        }
        catch (error) {
            error_utils_1.ErrorUtils.handleServiceError(this.logger, error, 'alterar status de favorito da ideia', userId);
        }
    }
};
exports.IdeaRepository = IdeaRepository;
exports.IdeaRepository = IdeaRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(database_provider_1.DATABASE_CONNECTION)),
    __metadata("design:paramtypes", [kysely_1.Kysely])
], IdeaRepository);
//# sourceMappingURL=idea.repository.js.map