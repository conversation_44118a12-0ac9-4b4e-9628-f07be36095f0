"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatForUser = exports.toUserTimezone = exports.fromUserTimezone = exports.TimezoneUtils = void 0;
const date_fns_tz_1 = require("date-fns-tz");
class TimezoneUtils {
    static fromUserTimezone(date, timezone) {
        return (0, date_fns_tz_1.fromZonedTime)(date, timezone);
    }
    static toUserTimezone(date, timezone) {
        return (0, date_fns_tz_1.toZonedTime)(date, timezone);
    }
    static formatInTimezone(date, timezone, formatStr = 'yyyy-MM-dd HH:mm:ss') {
        return (0, date_fns_tz_1.format)((0, date_fns_tz_1.toZonedTime)(date, timezone), formatStr, { timeZone: timezone });
    }
    static prepareDateForDatabase(date, timezone) {
        if (!date)
            return null;
        return this.fromUserTimezone(date, timezone);
    }
}
exports.TimezoneUtils = TimezoneUtils;
exports.fromUserTimezone = TimezoneUtils.fromUserTimezone;
exports.toUserTimezone = TimezoneUtils.toUserTimezone;
exports.formatForUser = TimezoneUtils.formatInTimezone;
//# sourceMappingURL=timezone.utils.js.map