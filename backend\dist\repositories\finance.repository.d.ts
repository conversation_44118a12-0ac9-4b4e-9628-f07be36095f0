import { Kysely } from 'kysely';
import { Database } from '../database.types';
import { BaseRepository } from './base.repository';
import { IFinanceRepository } from './interfaces/finance.repository.interface';
import { CreateFinanceDto } from '../finances/dto/create-finance.dto';
import { UpdateFinanceDto } from '../finances/dto/update-finance.dto';
import { FinanceResponseDto, FinanceSummaryDto } from '../finances/dto/finance-response.dto';
export declare class FinanceRepository extends BaseRepository<FinanceResponseDto, CreateFinanceDto, UpdateFinanceDto> implements IFinanceRepository {
    constructor(db: Kysely<Database>);
    get tableName(): keyof Database;
    get entityName(): string;
    mapToResponseDto(entity: any, userTimezone?: string): FinanceResponseDto;
    prepareCreateData(dto: CreateFinanceDto, userId: number, userTimezone?: string): any;
    prepareUpdateData(dto: UpdateFinanceDto, userTimezone?: string): any;
    findAllWithCategory(userId: number, userTimezone: string, page?: number, limit?: number): Promise<{
        data: FinanceResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    getSummary(userId: number, userTimezone: string, startDate?: Date, endDate?: Date): Promise<FinanceSummaryDto>;
    getExpensesByCategory(userId: number, startDate?: Date, endDate?: Date): Promise<{
        category: string;
        amount: number;
        count: number;
    }[]>;
    getAnnualSummary(userId: number, year: number, userTimezone: string): Promise<{
        year: number;
        monthlyData: {
            month: string;
            monthNumber: number;
            income: number;
            expenses: number;
            savings: number;
            balance: number;
        }[];
        totals: {
            income: number;
            expenses: number;
            savings: number;
            balance: number;
        };
    }>;
    getAnnualCategoryDistribution(userId: number, year: number, userTimezone: string): Promise<{
        name: string;
        value: number;
        color: string;
        percentage: number;
        count: number;
    }[]>;
}
