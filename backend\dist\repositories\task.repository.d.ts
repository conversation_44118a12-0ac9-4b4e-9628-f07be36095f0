import { Kysely } from 'kysely';
import { Database } from '../database.types';
import { BaseRepository } from './base.repository';
import { ITaskRepository } from './interfaces/task.repository.interface';
import { CreateTaskDto } from '../tasks/dto/create-task.dto';
import { UpdateTaskDto } from '../tasks/dto/update-task.dto';
import { TaskResponseDto } from '../tasks/dto/task-response.dto';
export declare class TaskRepository extends BaseRepository<TaskResponseDto, CreateTaskDto, UpdateTaskDto> implements ITaskRepository {
    constructor(db: Kysely<Database>);
    get tableName(): keyof Database;
    get entityName(): string;
    mapToResponseDto(entity: any, userTimezone?: string): TaskResponseDto;
    prepareCreateData(dto: CreateTaskDto, userId: number, userTimezone?: string): any;
    prepareUpdateData(dto: UpdateTaskDto, userTimezone?: string): any;
    findAllWithCategory(userId: number, userTimezone: string, page?: number, limit?: number, startDate?: string, endDate?: string): Promise<{
        data: TaskResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    complete(id: number, userId: number, userTimezone: string): Promise<TaskResponseDto>;
}
