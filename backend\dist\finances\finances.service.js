"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var FinancesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinancesService = void 0;
const common_1 = require("@nestjs/common");
const finance_repository_1 = require("../repositories/finance.repository");
const finance_category_repository_1 = require("../repositories/finance-category.repository");
let FinancesService = FinancesService_1 = class FinancesService {
    financeRepository;
    financeCategoryRepository;
    logger = new common_1.Logger(FinancesService_1.name);
    constructor(financeRepository, financeCategoryRepository) {
        this.financeRepository = financeRepository;
        this.financeCategoryRepository = financeCategoryRepository;
    }
    async create(createFinanceDto, userId, userTimezone) {
        return this.financeRepository.create(createFinanceDto, userId, userTimezone);
    }
    async findAll(userId, userTimezone, page = 1, limit = 50) {
        const result = await this.financeRepository.findAllWithCategory(userId, userTimezone, page, limit);
        return {
            finances: result.data,
            total: result.total,
            page: result.page,
            limit: result.limit
        };
    }
    async findOne(id, userId, userTimezone) {
        return this.financeRepository.findOne(id, userId, userTimezone);
    }
    async update(id, updateFinanceDto, userId, userTimezone) {
        return this.financeRepository.update(id, updateFinanceDto, userId, userTimezone);
    }
    async remove(id, userId) {
        return this.financeRepository.remove(id, userId);
    }
    async getSummary(userId, userTimezone, startDate, endDate) {
        return this.financeRepository.getSummary(userId, userTimezone, startDate, endDate);
    }
    async getExpensesByCategory(userId, startDate, endDate) {
        return this.financeRepository.getExpensesByCategory(userId, startDate, endDate);
    }
    async getAnnualSummary(userId, year, userTimezone) {
        return this.financeRepository.getAnnualSummary(userId, year, userTimezone);
    }
    async getAnnualCategoryDistribution(userId, year, userTimezone) {
        return this.financeRepository.getAnnualCategoryDistribution(userId, year, userTimezone);
    }
    async createCategory(createCategoryDto, userId) {
        return this.financeCategoryRepository.create(createCategoryDto, userId);
    }
    async findAllCategories(userId) {
        const result = await this.financeCategoryRepository.findAllOrderedByType(userId);
        return result;
    }
    async findOneCategory(id, userId) {
        return this.financeCategoryRepository.findOne(id, userId);
    }
    async updateCategory(id, updateCategoryDto, userId) {
        return this.financeCategoryRepository.update(id, updateCategoryDto, userId);
    }
    async removeCategory(id, userId) {
        const isInUse = await this.financeCategoryRepository.checkCategoryInUse(id, userId);
        if (isInUse) {
            throw new common_1.HttpException('Não é possível remover categoria que está sendo usada por transações', common_1.HttpStatus.BAD_REQUEST);
        }
        return this.financeCategoryRepository.remove(id, userId);
    }
};
exports.FinancesService = FinancesService;
exports.FinancesService = FinancesService = FinancesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [finance_repository_1.FinanceRepository,
        finance_category_repository_1.FinanceCategoryRepository])
], FinancesService);
//# sourceMappingURL=finances.service.js.map