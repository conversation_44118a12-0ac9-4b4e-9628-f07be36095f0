import { Kysely, MysqlDialect } from 'kysely';
import { createPool, createConnection, PoolOptions, Connection } from 'mysql2/promise';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { Database } from '../database.types';

export const DATABASE_CONNECTION = 'DATABASE_CONNECTION';
export const MYSQL2_CONNECTION = 'MYSQL2_CONNECTION';

interface ExtendedPoolOptions extends PoolOptions {
  acquireTimeout?: number;
  timeout?: number;
  reconnect?: boolean;
  keepAliveInitialDelay?: number;
  enableKeepAlive?: boolean;
  idleTimeout?: number;
  maxIdle?: number;
}

export const databaseProviders = [
  // Provider para Kysely
  {
    provide: DATABASE_CONNECTION,
    useFactory: (configService: ConfigService): Kysely<Database> => {
      const logger = new Logger('DatabaseProvider');

      // Suppress MySQL2 verbose logging unless explicitly enabled
      if (configService.get('MYSQL_VERBOSE_LOGS') !== 'true') {
        // Disable debug modules that might cause verbose output
        process.env.DEBUG = '';
        process.env.NODE_DEBUG = '';
      }

      // Configurações otimizadas para MySQL remoto
      const poolConfig: ExtendedPoolOptions = {
        host: configService.get('DB_HOST') || 'localhost',
        port: parseInt(configService.get('DB_PORT') || '3306'),
        user: configService.get('DB_USER') || 'root',
        password: configService.get('DB_PASSWORD'),
        database: configService.get('DB_DATABASE'),
        
        // Pool configuration optimized for remote MySQL
        connectionLimit: parseInt(configService.get('DB_CONNECTION_LIMIT') || '10'),
        acquireTimeout: parseInt(configService.get('DB_ACQUIRE_TIMEOUT') || '60000'), // 60s
        timeout: parseInt(configService.get('DB_TIMEOUT') || '60000'), // 60s
        
        // Keep-alive settings for remote connections
        reconnect: true,
        enableKeepAlive: true,
        keepAliveInitialDelay: 300000, // 5 minutes
        
        // Idle connection management
        idleTimeout: parseInt(configService.get('DB_IDLE_TIMEOUT') || '300000'), // 5 minutes
        maxIdle: parseInt(configService.get('DB_MAX_IDLE') || '5'),
        
        // Timezone
        timezone: configService.get('DB_TIMEZONE') || '+00:00',
        
        // SSL configuration for remote connections
        ssl: configService.get('DB_SSL') === 'true' ? {
          rejectUnauthorized: configService.get('DB_SSL_REJECT_UNAUTHORIZED') !== 'false'
        } : undefined,
        
        // Additional socket options for stability
        supportBigNumbers: true,
        bigNumberStrings: true,
        dateStrings: false,
        debug: configService.get('MYSQL_VERBOSE_LOGS') === 'true', // Only enable debug if explicitly requested
        trace: false, // Always disable packet tracing

        // Connection retry settings
        multipleStatements: false,

        // Character set
        charset: 'utf8mb4',
      };

      logger.log(`Connecting to MySQL at ${poolConfig.host}:${poolConfig.port}/${poolConfig.database}`);
      logger.log(`Pool configuration: connectionLimit=${poolConfig.connectionLimit}, acquireTimeout=${poolConfig.acquireTimeout}ms, timeout=${poolConfig.timeout}ms`);
      
      const pool = createPool(poolConfig);

      const dialect = new MysqlDialect({
        pool,
      });

      const kysely = new Kysely<Database>({
        dialect,
        log: configService.get('NODE_ENV') === 'development' ? ['query', 'error'] : ['error'],
      });
      
      // Test initial connection
      kysely.selectFrom('users')
        .select(['id'])
        .limit(1)
        .execute()
        .then(() => {
          logger.log('Database connection test successful');
        })
        .catch((error) => {
          logger.error('Database connection test failed:', error.message);
        });
      
      return kysely;
    },
    inject: [ConfigService],
  },
  // Provider para conexão MySQL2 direta
  {
    provide: MYSQL2_CONNECTION,
    useFactory: async (configService: ConfigService): Promise<Connection> => {
      const logger = new Logger('MySQL2ConnectionProvider');

      // Ensure MySQL2 logging is suppressed (unless explicitly enabled)
      if (configService.get('MYSQL_VERBOSE_LOGS') !== 'true') {
        // Disable debug modules that might cause verbose output
        process.env.DEBUG = '';
        process.env.NODE_DEBUG = '';
      }

      const connectionConfig = {
        host: configService.get('DB_HOST') || 'localhost',
        port: parseInt(configService.get('DB_PORT') || '3306'),
        user: configService.get('DB_USER') || 'root',
        password: configService.get('DB_PASSWORD'),
        database: configService.get('DB_DATABASE'),
        timezone: configService.get('DB_TIMEZONE') || '+00:00',
        charset: 'utf8mb4',
        supportBigNumbers: true,
        bigNumberStrings: true,
        dateStrings: false,
        debug: configService.get('MYSQL_VERBOSE_LOGS') === 'true', // Only enable debug if explicitly requested
        trace: false, // Always disable packet tracing
        multipleStatements: false,
      };
      
      try {
        const connection = await createConnection(connectionConfig);
        logger.log('MySQL2 direct connection established successfully');
        return connection;
      } catch (error) {
        logger.error('Failed to create MySQL2 direct connection:', error.message);
        throw error;
      }
    },
    inject: [ConfigService],
  },
];
