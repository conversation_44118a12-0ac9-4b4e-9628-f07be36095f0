import React, { useState } from 'react';
import { ArrowLeft, Search, Filter, ChevronDown, ChevronUp, X, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import MonthYearSelector from '../components/MonthYearSelector';
import CurrencyInput from '../components/CurrencyInput';
import { useFinances, useCreateFinance, useDeleteFinance, useFinanceCategories } from '../hooks/useFinances';
import { CreateFinanceDto } from '../types/api';

interface Income {
  id: string;
  description: string;
  amount: number;
  date: string;
  category: string;
}



const IncomePage: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [expandedId, setExpandedId] = useState<number | null>(null);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [filters, setFilters] = useState({
    category: '',
    startDate: '',
    endDate: '',
  });
  const [newIncome, setNewIncome] = useState({
    description: '',
    amount: 0,
    amountString: '',
    category_id: undefined as number | undefined
  });

  // Fetch data from API
  const { data: financesData, isLoading, error } = useFinances();
  const { data: categoriesData } = useFinanceCategories();
  const createFinanceMutation = useCreateFinance();
  const deleteFinanceMutation = useDeleteFinance();

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando receitas...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Erro ao carregar dados</h2>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar suas receitas. Tente novamente.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  // Filter incomes (transactions of type income, excluding savings withdrawals)
  const incomes = financesData?.finances?.filter(finance => 
    finance.transaction_type === 'income' && !finance.is_saving
  ) || [];
  const incomeCategories = categoriesData?.filter(cat => cat.transaction_type === 'income') || [];

  const filteredIncomes = incomes.filter(income => {
    const matchesSearch = (income.description || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !filters.category || income.category_name === filters.category;
    const incomeDate = new Date(income.transaction_date).toISOString().split('T')[0];
    const matchesDateRange = (!filters.startDate || incomeDate >= filters.startDate) &&
                           (!filters.endDate || incomeDate <= filters.endDate);

    return matchesSearch && matchesCategory && matchesDateRange;
  });

  const totalIncome = filteredIncomes.reduce((sum, income) => sum + parseFloat(income.amount), 0);

  const handleAddIncome = async () => {
    if (!newIncome.description.trim() || newIncome.amount <= 0) return;

    try {
      const incomeData: CreateFinanceDto = {
        transaction_type: 'income',
        description: newIncome.description,
        amount: newIncome.amountString,
        category_id: newIncome.category_id,
        transaction_date: new Date().toISOString()
      };

      await createFinanceMutation.mutateAsync(incomeData);
      setNewIncome({ description: '', amount: 0, amountString: '', category_id: undefined });
      setIsAddModalOpen(false);
    } catch (error) {
      console.error('Erro ao adicionar receita:', error);
    }
  };

  const toggleExpand = (id: number) => {
    setExpandedId(expandedId === id ? null : id);
  };

  const handleDelete = async (id: number) => {
    if (confirm('Tem certeza que deseja excluir este recebimento?')) {
      try {
        await deleteFinanceMutation.mutateAsync(id);
      } catch (error) {
        console.error('Erro ao deletar receita:', error);
      }
    }
  };

  const handleSave = (id: number) => {
    // Implementar edição se necessário
    // Edit income functionality
    setExpandedId(null);
  };

  const clearFilters = () => {
    setFilters({
      category: '',
      startDate: '',
      endDate: '',
    });
  };

  const handleAddTransaction = () => {
    setIsAddModalOpen(true);
  };

  return (
    <div className="min-h-screen bg-[#F7F7F7] pb-24 md:pb-6">
      {/* Header Padronizado Global */}
      <div className="fixed top-0 left-0 right-0 bg-[#F7F7F7] z-50">
        <div className="max-w-4xl mx-auto px-4 py-4 flex items-center justify-between">
          {/* Botão Voltar */}
          <button 
            onClick={() => navigate('/finances')}
            className="w-10 h-10 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
          >
            <ArrowLeft size={20} className="text-gray-600" />
          </button>

          {/* Título Centralizado */}
          <h1 className="text-xl font-bold text-gray-900">Receitas</h1>

          {/* Botão + */}
          <button
            onClick={handleAddTransaction}
            className="w-10 h-10 bg-[#212121] rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
          >
            <span className="text-white text-xl font-light">+</span>
          </button>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 pt-20 space-y-6">
        {/* Card Principal com Seletor de Data Integrado */}
        <div className="bg-white rounded-2xl p-6 shadow-sm">
          <div className="text-center">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">
              Total de Receitas
            </h2>
            
            {/* Seletor de Data Discreto */}
            <div className="flex justify-center mb-4">
              <MonthYearSelector
                selectedDate={selectedDate}
                onDateChange={setSelectedDate}
              />
            </div>
            
            {/* Valor Total */}
            <p className="text-4xl font-bold text-[#4CAF50] mb-4">
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
              }).format(totalIncome)}
            </p>
            
            {/* Métricas */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600">Total de transações</p>
                <p className="font-semibold text-gray-900">{filteredIncomes.length}</p>
              </div>
              <div>
                <p className="text-gray-600">Média por transação</p>
                <p className="font-semibold text-gray-900">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(filteredIncomes.length > 0 ? totalIncome / filteredIncomes.length : 0)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Lista de Receitas */}
        <div className="bg-white rounded-2xl p-6 shadow-sm">
          {/* Busca e Filtros */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search size={20} className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar receita..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full h-12 pl-12 pr-4 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
              />
            </div>
            <button 
              onClick={() => setIsFilterOpen(true)}
              className="h-12 px-6 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center gap-2"
            >
              <Filter size={20} className="text-gray-600" />
              <span>Filtros</span>
              {(filters.category || filters.startDate || filters.endDate) && (
                <span className="w-2 h-2 rounded-full bg-[#4CAF50]" />
              )}
            </button>
          </div>

          {/* Lista de Receitas */}
          <div className="space-y-4 max-h-[calc(100vh-24rem)] overflow-y-auto pr-2">
            {filteredIncomes.length > 0 ? (
              filteredIncomes.map(income => (
                <div
                  key={income.id}
                  className="overflow-hidden border border-gray-100 rounded-xl hover:border-[#4CAF50] transition-colors"
                >
                  <div className="p-4 flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="font-medium text-[#1C1C1C]">{income.description || 'Receita'}</h3>
                      <div className="flex items-center gap-4 mt-1">
                        <span className="text-sm text-[#6C6C6C]">
                          {new Date(income.transaction_date).toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' })}
                        </span>
                        {income.category_name && (
                          <span className="text-sm text-[#6C6C6C]">{income.category_name}</span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="font-medium text-[#4CAF50]">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(parseFloat(income.amount))}
                      </span>
                      <button
                        onClick={() => toggleExpand(income.id)}
                        className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                      >
                        {expandedId === income.id ? (
                          <ChevronUp size={20} className="text-gray-600" />
                        ) : (
                          <ChevronDown size={20} className="text-gray-600" />
                        )}
                      </button>
                    </div>
                  </div>

                  <AnimatePresence>
                    {expandedId === income.id && (
                      <div
                        className="border-t border-gray-100"
                      >
                        <div className="p-4 space-y-4">
                          <div>
                            <label className="block text-sm text-gray-600 mb-1">Valor</label>
                            <input
                              type="number"
                              value={parseFloat(income.amount)}
                              onChange={(e) => {}}
                              readOnly
                              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                              step="0.01"
                            />
                          </div>

                          <div>
                            <label className="block text-sm text-gray-600 mb-1">Categoria</label>
                            <select
                              value={income.category_name || ''}
                              onChange={(e) => {}}
                              disabled
                              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                            >
                              {incomeCategories.map(category => (
                                <option key={category.id} value={category.name}>
                                  {category.name}
                                </option>
                              ))}
                            </select>
                          </div>

                          <div>
                            <label className="block text-sm text-gray-600 mb-1">Data</label>
                            <input
                              type="date"
                              value={new Date(income.transaction_date).toISOString().split('T')[0]}
                              onChange={(e) => {}}
                              readOnly
                              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                            />
                          </div>

                          <div className="flex justify-end gap-2 pt-2">
                            <button
                              onClick={() => handleDelete(income.id)}
                              className="px-4 py-2 text-[#4CAF50] hover:bg-green-50 rounded-lg transition-colors"
                            >
                              Excluir
                            </button>
                            <button
                              onClick={() => setExpandedId(null)}
                              className="px-4 py-2 bg-[#4CAF50] text-white rounded-lg hover:bg-opacity-90 transition-colors"
                            >
                              Fechar
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </AnimatePresence>
                </div>
              ))
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Search size={24} className="text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhuma receita encontrada
                </h3>
                <p className="text-gray-500 text-sm">
                  {searchTerm || filters.category || filters.startDate || filters.endDate
                    ? 'Tente ajustar os filtros de busca'
                    : 'Suas receitas aparecerão aqui'
                  }
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modal de Filtros - Posicionamento Corrigido */}
      <AnimatePresence>
        {isFilterOpen && (
          <>
            {/* Backdrop Overlay */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[1000]"
              onClick={() => setIsFilterOpen(false)}
            />
            
            {/* Modal Container */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
              className="fixed inset-0 z-[1001] flex items-center justify-center p-4"
            >
              <div className="w-full max-w-md max-h-[90vh] bg-white rounded-2xl shadow-xl overflow-hidden flex flex-col">
                {/* Header */}
                <div className="flex-shrink-0 flex justify-between items-center p-6 border-b border-gray-100">
                  <h2 className="text-xl font-semibold text-gray-900">Filtros</h2>
                  <button
                    onClick={() => setIsFilterOpen(false)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <X size={20} className="text-gray-600" />
                  </button>
                </div>

                {/* Content */}
                <div className="flex-1 overflow-y-auto p-6 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Categoria
                    </label>
                    <select
                      value={filters.category}
                      onChange={(e) => setFilters({ ...filters, category: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                    >
                      <option value="">Todas as categorias</option>
                      {incomeCategories.map(category => (
                        <option key={category.id} value={category.name}>{category.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Data inicial
                    </label>
                    <input
                      type="date"
                      value={filters.startDate}
                      onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Data final
                    </label>
                    <input
                      type="date"
                      value={filters.endDate}
                      onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                    />
                  </div>
                </div>

                {/* Footer */}
                <div className="flex-shrink-0 flex gap-4 p-6 border-t border-gray-100">
                  <button
                    onClick={clearFilters}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Limpar
                  </button>
                  <button
                    onClick={() => setIsFilterOpen(false)}
                    className="flex-1 px-4 py-2 bg-[#4CAF50] text-white rounded-lg hover:bg-opacity-90 transition-colors"
                  >
                    Aplicar
                  </button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Add Income Modal */}
      <AnimatePresence>
        {isAddModalOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[1100]"
              onClick={() => setIsAddModalOpen(false)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="fixed inset-0 z-[1101] flex items-center justify-center p-4"
            >
              <div className="w-full max-w-md bg-white rounded-2xl p-6 shadow-xl">
                <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Nova Receita</h2>
                <button
                  onClick={() => setIsAddModalOpen(false)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X size={20} className="text-gray-600" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Descrição
                  </label>
                  <input
                    type="text"
                    value={newIncome.description}
                    onChange={(e) => setNewIncome({ ...newIncome, description: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                    placeholder="Ex: Salário, Freelance, etc."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Valor
                  </label>
                  <CurrencyInput
                    value={newIncome.amount}
                    onChange={(value, decimalString) => setNewIncome({ ...newIncome, amount: value, amountString: decimalString })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                    placeholder="0,00"
                    autoFocus
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Categoria
                  </label>
                  <select
                    value={newIncome.category_id || ''}
                    onChange={(e) => setNewIncome({ ...newIncome, category_id: e.target.value ? parseInt(e.target.value) : undefined })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                  >
                    <option value="">Selecione uma categoria</option>
                    {incomeCategories.map(category => (
                      <option key={category.id} value={category.id}>{category.name}</option>
                    ))}
                  </select>
                </div>

                <div className="flex gap-4 pt-4">
                  <button
                    onClick={() => setIsAddModalOpen(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleAddIncome}
                    disabled={createFinanceMutation.isPending || !newIncome.description.trim() || newIncome.amount <= 0}
                    className="flex-1 px-4 py-2 bg-[#4CAF50] text-white rounded-lg hover:bg-opacity-90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {createFinanceMutation.isPending ? 'Salvando...' : 'Salvar'}
                  </button>
                </div>
              </div>
            </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default IncomePage;