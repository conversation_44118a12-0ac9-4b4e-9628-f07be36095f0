// Dashboard Types
export interface UserSummaryDto {
  name: string;
  timezone: string;
}

export interface TasksSummaryDto {
  completed: number;
  total: number;
}

export interface FinancesSummaryDto {
  spent: number;
  budget: number;
  income: number;
  expenses: number;
  savings: number;
  hasAnnualGoal: boolean;
}

export interface IdeasSummaryDto {
  today: number;
  total: number;
  favorites: number;
}

export interface MonthlyProgressDto {
  appointments: {
    completed: number;
    total: number;
  };
  savings: number;
  ideas: number;
  tasksCompleted: number;
  financialGoalProgress: number;
}

export interface DashboardResponseDto {
  user: UserSummaryDto;
  tasks: TasksSummaryDto;
  finances: FinancesSummaryDto;
  ideas: IdeasSummaryDto;
  monthlyProgress: MonthlyProgressDto;
  currentMonth: string;
  currentYear: number;
}

// Task Types
export interface TaskResponseDto {
  id: number;
  task_type: 'appointment' | 'task';
  category_id?: number;
  category_name?: string;
  name: string;
  description?: string;
  task_date?: Date;
  user_id: number;
  completed_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface TaskListResponseDto {
  tasks: TaskResponseDto[];
  total: number;
  page: number;
  limit: number;
}

export interface CreateTaskDto {
  task_type: 'appointment' | 'task';
  category_id?: number;
  name: string;
  description?: string;
  task_date?: string;
}

export interface UpdateTaskDto {
  task_type?: 'appointment' | 'task';
  category_id?: number;
  name?: string;
  description?: string;
  task_date?: string;
}

// Task Category Types
export interface TaskCategoryResponseDto {
  id: number;
  name: string;
  user_id?: number;
  created_at: Date;
  updated_at: Date;
}

export interface CreateTaskCategoryDto {
  name: string;
}

export interface UpdateTaskCategoryDto {
  name?: string;
}

// Finance Types
export interface FinanceResponseDto {
  id: number;
  transaction_type: 'income' | 'expense';
  category_id?: number;
  category_name?: string;
  is_saving?: boolean;
  description?: string;
  amount: string;
  transaction_date: Date;
  user_id: number;
  created_at: Date;
  updated_at: Date;
}

export interface FinanceListResponseDto {
  finances: FinanceResponseDto[];
  total: number;
  page: number;
  limit: number;
}

export interface CreateFinanceDto {
  transaction_type: 'income' | 'expense';
  category_id?: number;
  is_saving?: boolean;
  description?: string;
  amount: string;
  transaction_date: string;
}

export interface UpdateFinanceDto {
  transaction_type?: 'income' | 'expense';
  category_id?: number;
  is_saving?: boolean;
  description?: string;
  amount?: string;
  transaction_date?: string;
}

export interface FinanceSummaryDto {
  totalIncome: number;
  totalExpenses: number;
  totalSavings: number;
  balance: number;
  period: {
    start: Date;
    end: Date;
  };
}

// Finance Category Types
export interface FinanceCategoryResponseDto {
  id: number;
  name: string;
  transaction_type: 'income' | 'expense';
  color?: string;
  user_id?: number;
  created_at: Date;
  updated_at: Date;
}

export interface CreateFinanceCategoryDto {
  name: string;
  transaction_type: 'income' | 'expense';
  color?: string;
}

export interface UpdateFinanceCategoryDto {
  name?: string;
  transaction_type?: 'income' | 'expense';
  color?: string;
}

// Idea Types
export interface IdeaResponseDto {
  id: number;
  category_id?: number;
  category_name?: string;
  name: string;
  description?: string;
  content?: string;
  is_favorite?: boolean;
  user_id: number;
  created_at: Date;
  updated_at: Date;
}

export interface IdeaListResponseDto {
  ideas: IdeaResponseDto[];
  total: number;
  page: number;
  limit: number;
}

export interface CreateIdeaDto {
  category_id?: number;
  name: string;
  description?: string;
  content?: string;
  is_favorite?: boolean;
}

export interface UpdateIdeaDto {
  category_id?: number;
  name?: string;
  description?: string;
  content?: string;
  is_favorite?: boolean;
}

// Idea Category Types
export interface IdeaCategoryResponseDto {
  id: number;
  name: string;
  user_id?: number;
  created_at: Date;
  updated_at: Date;
}

export interface CreateIdeaCategoryDto {
  name: string;
}

export interface UpdateIdeaCategoryDto {
  name?: string;
}

// Config Types
export interface UserConfigResponseDto {
  id: number;
  name: string;
  email: string;
  phone?: string;
  timezone: string;
  created_at: Date;
  updated_at: Date;
}

export interface UpdateUserConfigDto {
  name?: string;
  email?: string;
  phone?: string;
  timezone?: string;
}

export interface AnnualSavingsGoalResponseDto {
  id: number;
  year: number;
  amount: string;
  user_id?: number;
  created_at: Date;
  updated_at: Date;
}

export interface CreateAnnualSavingsGoalDto {
  year: number;
  amount: string;
}

export interface UpdateAnnualSavingsGoalDto {
  amount: string;
}

// Assistant Settings Types
export interface AssistantSettingsResponseDto {
  ai_humor: 'formal' | 'friendly' | 'casual' | 'professional';
  response_size: 'short' | 'medium' | 'long' | 'detailed';
  reminder_time: string; // Formato HH:MM
  reminder_interval: string; // Intervalo em minutos
}

export interface UpdateAssistantSettingsDto {
  ai_humor?: 'formal' | 'friendly' | 'casual' | 'professional';
  response_size?: 'short' | 'medium' | 'long' | 'detailed';
  reminder_time?: string; // Formato HH:MM
  reminder_interval?: string; // Intervalo em minutos
}

// WhatsApp Integration Types
export interface WhatsAppIntegrationResponseDto {
  id: number;
  status: 'pending' | 'active' | 'inactive';
  phone?: string | null;
  is_validated: boolean;
  activation_code?: string | null;
  user_id: number;
  created_at: Date;
  updated_at: Date;
}

export interface CreateWhatsAppIntegrationDto {
  // Phone não é mais obrigatório na criação, será gerado um código de ativação
}

export interface ValidateWhatsAppIntegrationDto {
  activation_code: string;
  phone: string;
}

export interface UpdateWhatsAppIntegrationDto {
  status?: 'pending' | 'active' | 'inactive';
  phone?: string;
  is_validated?: boolean;
}

// Common Types
export interface ApiError {
  message: string;
  statusCode: number;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  start_date?: string;
  end_date?: string;
}

// Annual Financial Summary Types
export interface MonthlyFinancialData {
  month: string;
  monthNumber: number;
  income: number;
  expenses: number;
  savings: number;
  balance: number;
}

export interface AnnualFinancialTotals {
  income: number;
  expenses: number;
  savings: number;
  balance: number;
}

export interface AnnualFinancialAverages {
  monthlyIncome: number;
  monthlyExpenses: number;
  monthlySavings: number;
}

export interface AnnualFinancialMetrics {
  savingsRate: number;
  goalAmount: number;
  goalProgress: number;
}

export interface AnnualFinancialHighlights {
  bestSavingsMonth: {
    month: string;
    savings: number;
  };
  worstSavingsMonth: {
    month: string;
    savings: number;
  };
  highestIncomeMonth: {
    month: string;
    income: number;
  };
  highestExpenseMonth: {
    month: string;
    expenses: number;
  };
}

export interface AnnualFinancialSummaryDto {
  year: number;
  monthlyData: MonthlyFinancialData[];
  totals: AnnualFinancialTotals;
  averages: AnnualFinancialAverages;
  metrics: AnnualFinancialMetrics;
  highlights: AnnualFinancialHighlights;
}

export interface CategoryDistributionDto {
  name: string;
  value: number;
  color: string;
  percentage: number;
}

export interface AnnualComparisonDto {
  category: string;
  current: number;
  previous: number;
  change: number;
}
