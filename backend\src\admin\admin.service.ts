import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
} from '@nestjs/common';
import { Connection } from 'mysql2/promise';
import { MYSQL2_CONNECTION } from '../database/database.provider';
import {
  AdminUserListResponseDto,
  AdminDashboardStatsDto,
  UpdatePlanDto,
  UpdatePaymentSettingsDto,
  UpdateUserDto,
} from './dto/admin.dto';

@Injectable()
export class AdminService {
  constructor(@Inject(MYSQL2_CONNECTION) private connection: Connection) {}

  // Estatísticas gerais do dashboard admin
  async getDashboardStats(): Promise<AdminDashboardStatsDto> {
    // Total de usuários
    const [totalUsersResult] = await this.connection.execute(
      'SELECT COUNT(*) as count FROM users WHERE deleted_at IS NULL'
    );
    const totalUsers = (totalUsersResult as any[])[0].count;

    // Assinaturas ativas
    const [activeSubsResult] = await this.connection.execute(
      'SELECT COUNT(*) as count FROM user_subscriptions WHERE status = ?',
      ['active']
    );
    const activeSubscriptions = (activeSubsResult as any[])[0].count;

    // Receita total (aproximada - assumindo preços dos planos)
    const [revenueResult] = await this.connection.execute(
      `SELECT SUM(p.price) as total
       FROM user_subscriptions us
       INNER JOIN plans p ON us.plan_id = p.id
       WHERE us.status = 'active'`
    );
    const totalRevenue = parseFloat((revenueResult as any[])[0]?.total || '0');

    // Receita mensal (últimos 30 dias)
    const [monthlyRevenueResult] = await this.connection.execute(
      `SELECT SUM(p.price) as monthly
       FROM user_subscriptions us
       INNER JOIN plans p ON us.plan_id = p.id
       WHERE us.status = 'active' AND us.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)`
    );
    const monthlyRevenue = parseFloat((monthlyRevenueResult as any[])[0]?.monthly || '0');

    // Assinaturas canceladas
    const [canceledSubsResult] = await this.connection.execute(
      'SELECT COUNT(*) as count FROM user_subscriptions WHERE status = ?',
      ['canceled']
    );
    const cancelledSubscriptions = (canceledSubsResult as any[])[0].count;

    return {
      total_users: totalUsers,
      active_subscriptions: activeSubscriptions,
      total_revenue: totalRevenue,
      monthly_revenue: monthlyRevenue,
      cancelled_subscriptions: cancelledSubscriptions,
    };
  }

  // Listar usuários com paginação
  async getUsers(page = 1, limit = 20): Promise<{
    users: AdminUserListResponseDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    const offset = (page - 1) * limit;

    // Buscar usuários com suas assinaturas
    const [rows] = await this.connection.execute(
      `SELECT 
         u.id, u.name, u.email, u.phone, u.timezone, u.is_admin, u.created_at,
         us.id as subscription_id, p.name as plan_name, us.status as subscription_status,
         us.current_period_end
       FROM users u
       LEFT JOIN user_subscriptions us ON u.id = us.user_id AND us.status = 'active'
       LEFT JOIN plans p ON us.plan_id = p.id
       WHERE u.deleted_at IS NULL
       ORDER BY u.created_at DESC
       LIMIT ? OFFSET ?`,
      [limit, offset]
    );

    // Contar total
    const [countResult] = await this.connection.execute(
      'SELECT COUNT(*) as total FROM users WHERE deleted_at IS NULL'
    );
    const total = (countResult as any[])[0].total;

    const users = (rows as any[]).map(row => ({
      id: row.id,
      name: row.name,
      email: row.email,
      phone: row.phone,
      timezone: row.timezone,
      is_admin: row.is_admin === 1,
      created_at: row.created_at,
      subscription: row.subscription_id ? {
        id: row.subscription_id,
        plan_name: row.plan_name,
        status: row.subscription_status,
        current_period_end: row.current_period_end,
      } : null,
    }));

    return {
      users,
      total,
      page,
      limit,
    };
  }

  // Atualizar usuário
  async updateUser(userId: number, updateDto: UpdateUserDto): Promise<void> {
    const updateFields: string[] = [];
    const values: any[] = [];

    if (updateDto.name) {
      updateFields.push('name = ?');
      values.push(updateDto.name);
    }
    if (updateDto.email) {
      updateFields.push('email = ?');
      values.push(updateDto.email);
    }
    if (updateDto.phone) {
      updateFields.push('phone = ?');
      values.push(updateDto.phone);
    }
    if (typeof updateDto.is_admin === 'boolean') {
      updateFields.push('is_admin = ?');
      values.push(updateDto.is_admin ? 1 : 0);
    }

    if (updateFields.length === 0) {
      throw new BadRequestException('Nenhum campo para atualizar');
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(userId);

    const [result] = await this.connection.execute(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ? AND deleted_at IS NULL`,
      values
    );

    if ((result as any).affectedRows === 0) {
      throw new NotFoundException('Usuário não encontrado');
    }
  }

  // Listar planos (para admin)
  async getPlans() {
    const [rows] = await this.connection.execute(
      `SELECT id, name, slug, description, price, currency, billing_period,
              stripe_price_id, features, is_active, sort_order, created_at
       FROM plans
       ORDER BY sort_order ASC, price ASC`
    );

    return (rows as any[]).map(row => ({
      ...row,
      price: parseFloat(row.price),
      features: typeof row.features === 'string' ? JSON.parse(row.features) : row.features,
      is_active: row.is_active === 1,
    }));
  }

  // Atualizar plano
  async updatePlan(planId: number, updateDto: UpdatePlanDto): Promise<void> {
    const updateFields: string[] = [];
    const values: any[] = [];

    if (updateDto.name) {
      updateFields.push('name = ?');
      values.push(updateDto.name);
    }
    if (updateDto.description) {
      updateFields.push('description = ?');
      values.push(updateDto.description);
    }
    if (updateDto.price) {
      updateFields.push('price = ?');
      values.push(updateDto.price);
    }
    if (updateDto.stripe_price_id) {
      updateFields.push('stripe_price_id = ?');
      values.push(updateDto.stripe_price_id);
    }
    if (updateDto.features) {
      updateFields.push('features = ?');
      values.push(JSON.stringify(updateDto.features));
    }
    if (typeof updateDto.is_active === 'boolean') {
      updateFields.push('is_active = ?');
      values.push(updateDto.is_active ? 1 : 0);
    }
    if (updateDto.sort_order) {
      updateFields.push('sort_order = ?');
      values.push(updateDto.sort_order);
    }

    if (updateFields.length === 0) {
      throw new BadRequestException('Nenhum campo para atualizar');
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(planId);

    const [result] = await this.connection.execute(
      `UPDATE plans SET ${updateFields.join(', ')} WHERE id = ?`,
      values
    );

    if ((result as any).affectedRows === 0) {
      throw new NotFoundException('Plano não encontrado');
    }
  }

  // Configurações de pagamento
  async getPaymentSettings() {
    const [rows] = await this.connection.execute(
      'SELECT * FROM payment_settings WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1'
    );

    if (!Array.isArray(rows) || rows.length === 0) {
      return null;
    }

    const settings = rows[0] as any;
    return {
      id: settings.id,
      stripe_public_key: settings.stripe_public_key,
      // Não retornar chave secreta por segurança
      stripe_secret_key: settings.stripe_secret_key ? '***' : null,
      stripe_webhook_secret: settings.stripe_webhook_secret ? '***' : null,
      is_active: settings.is_active === 1,
      created_at: settings.created_at,
      updated_at: settings.updated_at,
    };
  }

  // Atualizar configurações de pagamento
  async updatePaymentSettings(updateDto: UpdatePaymentSettingsDto): Promise<void> {
    // Primeiro, desativar configurações antigas
    await this.connection.execute(
      'UPDATE payment_settings SET is_active = 0'
    );

    // Criar nova configuração
    const [result] = await this.connection.execute(
      `INSERT INTO payment_settings 
       (stripe_public_key, stripe_secret_key, stripe_webhook_secret, is_active)
       VALUES (?, ?, ?, ?)`,
      [
        updateDto.stripe_public_key || null,
        updateDto.stripe_secret_key || null,
        updateDto.stripe_webhook_secret || null,
        updateDto.is_active !== false ? 1 : 0,
      ]
    );
  }

  // Obter configurações do Stripe
  async getStripeConfig() {
    const [rows] = await this.connection.execute(
      'SELECT * FROM payment_settings WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1'
    );

    if (!Array.isArray(rows) || rows.length === 0) {
      return null;
    }

    const settings = rows[0] as any;
    return {
      public_key: settings.stripe_public_key,
      secret_key: settings.stripe_secret_key ? '***' : null, // Mascarar por segurança
      webhook_endpoint_secret: settings.stripe_webhook_secret ? '***' : null,
      test_mode: true, // Por padrão sempre teste até ser configurado
    };
  }

  // Salvar configurações do Stripe
  async saveStripeConfig(config: {
    public_key: string;
    secret_key: string;
    webhook_endpoint_secret?: string;
    test_mode: boolean;
  }): Promise<void> {
    // Validar formato das chaves
    const publicKeyPrefix = config.test_mode ? 'pk_test_' : 'pk_live_';
    const secretKeyPrefix = config.test_mode ? 'sk_test_' : 'sk_live_';

    if (!config.public_key.startsWith(publicKeyPrefix)) {
      throw new BadRequestException(
        `Chave pública deve começar com '${publicKeyPrefix}'`
      );
    }

    if (!config.secret_key.startsWith(secretKeyPrefix)) {
      throw new BadRequestException(
        `Chave secreta deve começar com '${secretKeyPrefix}'`
      );
    }

    // Primeiro, desativar configurações antigas
    await this.connection.execute(
      'UPDATE payment_settings SET is_active = 0'
    );

    // Inserir nova configuração
    await this.connection.execute(
      `INSERT INTO payment_settings 
       (stripe_public_key, stripe_secret_key, stripe_webhook_secret, is_active)
       VALUES (?, ?, ?, ?)`,
      [
        config.public_key,
        config.secret_key,
        config.webhook_endpoint_secret || null,
        1, // Ativar a nova configuração
      ]
    );
  }

  // Testar conexão com Stripe
  async testStripeConnection(config: {
    secret_key: string;
    test_mode: boolean;
  }): Promise<{ message: string }> {
    try {
      // Simular teste de conexão com Stripe
      // Em um ambiente real, você faria uma chamada para a API do Stripe
      
      const secretKeyPrefix = config.test_mode ? 'sk_test_' : 'sk_live_';
      
      if (!config.secret_key.startsWith(secretKeyPrefix)) {
        throw new BadRequestException(
          `Chave secreta deve começar com '${secretKeyPrefix}' para o modo ${config.test_mode ? 'teste' : 'produção'}`
        );
      }

      // Aqui você poderia fazer uma chamada real para o Stripe:
      // const stripe = new Stripe(config.secret_key);
      // await stripe.accounts.retrieve();
      
      return {
        message: `Conexão com Stripe testada com sucesso (modo ${config.test_mode ? 'teste' : 'produção'})!`
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Erro ao conectar com Stripe: ' + error.message);
    }
  }
}
