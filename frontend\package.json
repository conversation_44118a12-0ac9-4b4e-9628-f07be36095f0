{"name": "project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "typecheck": "tsc --noEmit"}, "dependencies": {"@supabase/supabase-js": "^2.39.7", "@tanstack/react-query": "^5.80.7", "clsx": "^2.1.0", "date-fns": "^3.3.1", "framer-motion": "^11.0.8", "ky": "^1.8.1", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.2", "react-toastify": "^11.0.5", "recharts": "^2.12.2", "tailwind-merge": "^2.2.1", "uuid": "^11.1.0", "zustand": "^4.5.2"}, "devDependencies": {"@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.1.4", "vitest": "^1.3.1"}}